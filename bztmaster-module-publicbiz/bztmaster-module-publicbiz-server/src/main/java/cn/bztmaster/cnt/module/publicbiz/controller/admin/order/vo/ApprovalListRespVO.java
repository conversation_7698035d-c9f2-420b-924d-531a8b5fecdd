package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 审批列表 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 审批列表 Response VO")
@Data
public class ApprovalListRespVO {

    @Schema(description = "审批ID", example = "1")
    private Long approvalId;

    @Schema(description = "审批单号", example = "APV123456789")
    private String approvalNo;

    @Schema(description = "审批类型", example = "order_approval")
    private String approvalType;

    @Schema(description = "审批级别", example = "1")
    private Integer approvalLevel;

    @Schema(description = "审批结果", example = "pending")
    private String approvalResult;

    @Schema(description = "审批意见", example = "等待审批")
    private String approvalOpinion;

    @Schema(description = "审批人姓名", example = "待分配")
    private String approverName;

    @Schema(description = "审批时间", example = "2024-06-01 10:00:00")
    private String approvalTime;
}




package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 导出请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 导出请求 VO")
@Data
public class ExportReqVO {

    @Schema(description = "导出类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "excel")
    @NotBlank(message = "导出类型不能为空")
    private String exportType;

    @Schema(description = "订单状态", example = "all")
    private String orderStatus;

    @Schema(description = "开始日期", example = "2024-06-01")
    private String startDate;

    @Schema(description = "结束日期", example = "2024-06-30")
    private String endDate;
}




### 门店排班页面（pages/agency-workbench/agency-schedule.vue）后端接口文档提示词

> 目的：基于现有页面需求，定义可直接生成后端代码的接口契约与实现要点，支持按机构查询阿姨与当月任务，前端按“阿姨 x 天”分组展示汇总色块，点击某天时拉取该阿姨当天任务列表。

---

### 场景背景
- 页面入口：门店排班（机构端）。
- 进入页面后：
  - 需要拿到当前机构 `agencyId`；
  - 根据 `agencyId` 查询机构下阿姨基本信息；
  - 关联家政服务任务表，查询该机构下阿姨在“指定年月”的所有任务；
  - 后端返回“按阿姨 + 天”的分组汇总（用于前端渲染单元格色块：0=透明、1-3=绿色、>3=橙色）；
  - 当用户点击某天单元格时，再拉取该阿姨当天的任务明细列表。

---

### 认证与多租户
- 通过登录态（JWT/Session）识别当前用户与 `tenant_id`；接口均需校验 `tenant_id` 一致性。
- `agencyId` 可从登录上下文或明确的查询参数获得；若从 token 解析，请提供接口1以便前端在需要时显式获取。

---

### 表结构（供实现参考）
- 阿姨表：`publicbiz_practitioner`
  - 关键字段：`id`(PK), `tenant_id`, `aunt_oneid`, `name`, `phone`, `avatar`, `current_status`, `agency_id`, `deleted`
- 任务表：`publicbiz_domestic_task`
  - 关键字段：`id`(PK), `tenant_id`, `task_no`, `task_name`, `task_type`, `task_status`, `planned_start_time`, `planned_end_time`, `duration`, `practitioner_oneid`, `practitioner_name`, `schedule_date`, `customer_name`, `customer_phone`, `service_address`, `service_category_name`, `deleted`

---

### 接口二：查询机构阿姨列表（轻量信息）
- **Method**: GET
- **Path**: /publicbiz/agency/{agencyId}/aunts
- **Auth**: 必须
- **Query**: 
  - `status` 可选：`active|inactive|pending`，默认返回 `active`
  - `keyword` 可选：按姓名/手机号模糊
- **Response**:
```json
{
  "code": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "auntOneId": "GUID-xxx",
      "name": "王阿姨",
      "phone": "138****1234",
      "avatar": "https://...",
      "currentStatus": "服务中",
      "platformStatus": "cooperating"
    }
  ]
}
```
- **实现要点**：
  - 仅返回页面列表所需字段，减少负载。
  - 过滤条件：`tenant_id`、`agency_id`、`deleted=0`、`status`。

---

### 接口三：查询当月排班汇总（按阿姨 + 天分组）
- 用于前端渲染色块，不需要返回全部任务详情，只需当天任务数量及轻量标识。
- **Method**: GET
- **Path**: /publicbiz/agency/{agencyId}/schedules/month
- **Auth**: 必须
- **Query**:
  - `year` 必填：如 2025
  - `month` 必填：1-12
- **Response（示例）**：
```json
{
  "code": 0,
  "msg": "OK",
  "data": {
    "year": 2025,
    "month": 7,
    "aunts": [
      { "id": 1, "auntOneId": "GUID-1", "name": "王阿姨" },
      { "id": 2, "auntOneId": "GUID-2", "name": "李阿姨" }
    ],
    "schedules": {
      "1": {                
        "1": { "taskCount": 1 },
        "3": { "taskCount": 2 },
        "7": { "taskCount": 4 }
      },
      "2": {
        "2": { "taskCount": 1 },
        "6": { "taskCount": 2 }
      }
    }
  }
}
```
- **说明**：
  - `aunts` 为参与排班的阿姨清单（建议与接口二数据共享/一致）。
  - `schedules[auntId][day].taskCount` 为当天任务数量；前端规则：0=透明、1-3=绿色、>3=橙色。
  - 任务统计范围：`schedule_date` 落在指定 `year-month` 且 `task_status != cancelled`（可调）。
- **实现要点**：
  - 通过 `publicbiz_domestic_task` 关联 `publicbiz_practitioner`（以 `practitioner_oneid = aunt_oneid`）过滤当前 `agencyId`、`tenant_id`、`deleted=0`。
  - 聚合 SQL 返回按天计数；后端将日期转换为 day(1..31) 作为键。

---

### 接口四：查询阿姨某天任务明细
- 点击单元格后拉取任务列表，按开始时间排序。
- **Method**: GET
- **Path**: /publicbiz/agency/{agencyId}/aunts/{auntId}/schedules/day
- **Auth**: 必须
- **Query**:
  - `date` 必填：`yyyy-MM-dd`
- **Response（示例）**：
```json
{
  "code": 0,
  "msg": "OK",
  "data": [
    {
      "id": 789,
      "taskNo": "TASK20250707001",
      "serviceType": "日常保洁",               
      "date": "2025-07-07",                   
      "time": "08:00-11:00",                  
      "duration": "3h",                       
      "customer": "李女士",
      "customerPhone": "138****5678",
      "address": "朝阳区BB小区",
      "taskStatus": "assigned"
    }
  ]
}
```
- **字段映射建议**：
  - `serviceType` ← `service_category_name` 或 `task_name`（以产品口径为准）
  - `date` ← `schedule_date`
  - `time` ← 由 `planned_start_time` 与 `planned_end_time` 格式化拼接（若为空则用 `duration`）
  - `duration` ← `duration`
  - `customer` ← `customer_name`
  - `customerPhone` ← `customer_phone`
  - `address` ← `service_address`
- **实现要点**：
  - 校验 `auntId` 与 `aunt_oneid` 的关系：可先查 `publicbiz_practitioner.id -> aunt_oneid`，再以 `practitioner_oneid` 查询任务。
  - 排序：`planned_start_time` 升序；若为空可按 `id` 升序兜底。

---

### SQL 参考（MySQL）

1) 当月汇总（计数）：
```sql
-- 输入：:tenantId, :agencyId, :year, :month
-- 说明：按阿姨 oneid + 天聚合任务数量（排除 deleted=1 / 取消任务可选）
SELECT p.id              AS aunt_id,
       p.aunt_oneid      AS aunt_oneid,
       DAY(t.schedule_date) AS day_of_month,
       COUNT(1)          AS task_count
FROM publicbiz_domestic_task t
JOIN publicbiz_practitioner p
  ON p.aunt_oneid = t.practitioner_oneid
WHERE p.tenant_id = :tenantId
  AND p.agency_id = :agencyId
  AND p.deleted = b'0'
  AND t.tenant_id = :tenantId
  AND t.deleted = b'0'
  AND DATE_FORMAT(t.schedule_date,'%Y') = :year
  AND DATE_FORMAT(t.schedule_date,'%c') = :month
  AND t.task_status <> 'cancelled'
GROUP BY p.id, p.aunt_oneid, DAY(t.schedule_date);
```

2) 某天明细：
```sql
-- 输入：:tenantId, :agencyId, :auntId, :date
-- 步骤A：查 oneid
SELECT aunt_oneid FROM publicbiz_practitioner
 WHERE tenant_id=:tenantId AND id=:auntId AND agency_id=:agencyId AND deleted=b'0';

-- 步骤B：查任务
SELECT id,
       task_no,
       task_name,
       task_type,
       task_status,
       planned_start_time,
       planned_end_time,
       duration,
       service_category_name,
       customer_name,
       customer_phone,
       service_address,
       schedule_date
  FROM publicbiz_domestic_task
 WHERE tenant_id = :tenantId
   AND deleted = b'0'
   AND practitioner_oneid = :auntOneId
   AND schedule_date = :date
 ORDER BY planned_start_time ASC, id ASC;
```

---

### 错误码约定
- `0` 成功
- `40001` 参数缺失或非法
- `40301` 无权限/租户或机构不匹配
- `40401` 机构不存在或无阿姨
- `50001` 服务器内部错误

---

### 性能与索引建议
- 索引：
  - `publicbiz_domestic_task(practitioner_oneid, schedule_date, tenant_id, deleted)` 复合索引
  - `publicbiz_practitioner(agency_id, tenant_id, deleted)` 复合索引
- 分页：
  - 月汇总不分页；
  - 当天任务明细数量通常有限，无需分页；若单日任务量大于 200，可加分页参数。

---

### 安全与合规
- 所有接口校验 `tenant_id` 与登录态一致；
- 只允许访问本机构数据（`agencyId` 校验）；
- 返回时对手机号做脱敏（如需）。

---

### 前端对接契约（与 agency-schedule.vue 对齐）
- 列表渲染：
  - 使用“接口二”或“接口三”中的 `aunts` 作为左侧阿姨列。
- 色块：
  - 使用“接口三” `schedules[auntId][day].taskCount` 判定：
    - 0 → `no-schedule`
    - 1-3 → `low-task-count`
    - >3 → `high-task-count`
- 点击单元格：
  - 调“接口四”带上 `agencyId`, `auntId`, `date`，将响应映射到：
    - `serviceType` → 页面 `service-type`
    - `duration` → 页面 `duration-tag`
    - `customer/customerPhone/address/date/time` → 页面明细文案

---

### 示例请求/响应
1) 当月汇总
```
GET /publicbiz/agency/10086/schedules/month?year=2025&month=7
```
返回见“接口三”。

2) 当天明细
```
GET /publicbiz/agency/10086/aunts/1/schedules/day?date=2025-07-07
```
返回见“接口四”。

---

### 可生成代码的实现提示（伪指令）
- 控制器层：按照上述 Path/Method 定义 4 个接口；统一响应结构 `{code,msg,data}`。
- 服务层：
  - 校验 `tenant_id`、`agencyId`；
  - 通过仓储执行上述 SQL/QueryDSL；
  - 将 `schedule_date` 聚合为 day-of-month 数字键；
  - 将任务行映射为前端期望字段；
- 异常处理：
  - 参数校验失败抛业务异常 -> `40001`；
  - 无数据不视为异常，返回空数组或空对象；
  - 非预期异常 -> `50001`。



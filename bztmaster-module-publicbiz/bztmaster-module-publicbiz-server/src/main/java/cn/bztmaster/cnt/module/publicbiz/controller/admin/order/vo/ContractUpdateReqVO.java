package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 合同更新 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 合同更新 Request VO")
@Data
public class ContractUpdateReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "合同状态", example = "signed")
    private String contractStatus;

    @Schema(description = "合同备注", example = "合同信息已更新")
    private String contractRemark;
}




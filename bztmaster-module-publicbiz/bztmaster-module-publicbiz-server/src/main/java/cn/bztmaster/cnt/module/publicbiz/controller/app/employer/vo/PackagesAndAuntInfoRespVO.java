package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 根据订单号获取套餐和阿姨信息 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "根据订单号获取套餐和阿姨信息 Response VO")
@Data
public class PackagesAndAuntInfoRespVO {

    @Schema(description = "套餐主图URL", example = "https://qiniu.bzmaster.cn/20250730/package_main_image.jpg")
    private String packageMainImage;

    @Schema(description = "套餐名称", example = "临时保洁套餐A3")
    private String packageName;

    @Schema(description = "阿姨名称", example = "张阿姨")
    private String auntName;

    @Schema(description = "阿姨oneID", example = "aunt_123456")
    private String auntOneId;

    @Schema(description = "服务套餐ID", example = "package_789")
    private String servicePackageId;

    @Schema(description = "机构ID", example = "agency_456")
    private String agencyId;

    @Schema(description = "机构名称", example = "测试机构A")
    private String agencyName;
}

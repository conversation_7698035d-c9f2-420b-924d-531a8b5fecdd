package cn.bztmaster.cnt.module.publicbiz.service.auth.impl;

import cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.util.servlet.ServletUtils;
import cn.bztmaster.cnt.framework.common.util.monitor.TracerUtils;
import cn.bztmaster.cnt.framework.common.enums.UserTypeEnum;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.app.auth.vo.*;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.WxCode2SessionRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.MpUserMapper;
import cn.bztmaster.cnt.module.publicbiz.service.auth.EmployerAuthService;
import cn.bztmaster.cnt.module.publicbiz.service.employer.WxMiniProgramService;
import cn.bztmaster.cnt.module.publicbiz.service.file.FileUploadService;
import cn.bztmaster.cnt.module.publicbiz.framework.config.WxMiniProgramConfig;
import cn.bztmaster.cnt.framework.common.biz.system.oauth2.OAuth2TokenCommonApi;
import cn.bztmaster.cnt.framework.common.biz.system.oauth2.dto.OAuth2AccessTokenCreateReqDTO;
import cn.bztmaster.cnt.framework.common.biz.system.oauth2.dto.OAuth2AccessTokenRespDTO;
import cn.bztmaster.cnt.module.system.api.logger.LoginLogApi;
import cn.bztmaster.cnt.module.system.api.logger.dto.LoginLogCreateReqDTO;
import cn.bztmaster.cnt.module.system.api.sms.SmsCodeApi;
import cn.bztmaster.cnt.module.system.api.sms.dto.code.SmsCodeSendReqDTO;
import cn.bztmaster.cnt.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import cn.bztmaster.cnt.module.system.api.sms.dto.code.SmsCodeValidateReqDTO;
import cn.bztmaster.cnt.module.system.enums.logger.LoginLogTypeEnum;
import cn.bztmaster.cnt.module.system.enums.logger.LoginResultEnum;
import cn.bztmaster.cnt.module.system.enums.oauth2.OAuth2ClientConstants;
import cn.bztmaster.cnt.module.system.enums.sms.SmsSceneEnum;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.annotation.PostConstruct;
import java.time.LocalDateTime;

import static cn.bztmaster.cnt.framework.common.exception.enums.GlobalErrorCodeConstants.BAD_REQUEST;
import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 雇主端授权登录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class EmployerAuthServiceImpl implements EmployerAuthService {

    @Resource
    private WxMiniProgramService wxMiniProgramService;

    @Resource
    private MpUserMapper mpUserMapper;

    @Resource
    private SmsCodeApi smsCodeApi;

    @Resource
    private OAuth2TokenCommonApi oauth2TokenApi;

    @Resource
    private LoginLogApi loginLogApi;

    @Resource
    private WxMiniProgramConfig wxMiniProgramConfig;

    @Resource
    private FileUploadService fileUploadService;

    // 添加初始化日志
    @PostConstruct
    public void init() {
        log.info("EmployerAuthServiceImpl 初始化完成");
        log.info("SmsCodeApi: {}", smsCodeApi != null ? "已注入" : "未注入");
        log.info("OAuth2TokenCommonApi: {}", oauth2TokenApi != null ? "已注入" : "未注入");
        log.info("LoginLogApi: {}", loginLogApi != null ? "已注入" : "未注入");
        log.info("WxMiniProgramConfig: {}", wxMiniProgramConfig != null ? "已注入" : "未注入");
    }

    @Override
    @Transactional
    public EmployerAuthLoginRespVO weixinMiniAppLogin(EmployerAuthWeixinMiniAppLoginReqVO reqVO) {
        // 记录使用的微信小程序配置
        log.info("使用微信小程序配置 - AppId: {}, Secret: {}",
                wxMiniProgramConfig.getAppId(),
                wxMiniProgramConfig.getSecret().substring(0, 8) + "****");

        // 1. 调用微信接口获取openid
        WxCode2SessionRespVO wxResult = wxMiniProgramService.code2Session(reqVO.getCode());
        if (!wxResult.isSuccess()) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "微信登录失败: " + wxResult.getErrmsg());
        }

        // 2. 获取用户授权的手机号
        String authorizedMobile = null;
        if (StrUtil.isNotBlank(reqVO.getPhoneCode())) {
            authorizedMobile = wxMiniProgramService.getPhoneNumber(reqVO.getPhoneCode());
            if (authorizedMobile != null) {
                log.info("成功获取用户授权手机号: {}", authorizedMobile);
            } else {
                log.warn("获取用户授权手机号失败，phoneCode: {}", reqVO.getPhoneCode());
            }
        }

        // 3. 处理微信头像上传
        String uploadedHeadImageUrl = null;
        if (StrUtil.isNotBlank(reqVO.getHeadImageUrl())) {
            uploadedHeadImageUrl = uploadWeixinHeadImage(reqVO.getHeadImageUrl());
        }

        // 4. 根据openid查找或创建用户
        MpUserDO user = mpUserMapper.selectByOpenid(wxResult.getOpenid());
        if (user == null) {
            // 创建新用户
            user = new MpUserDO();
            user.setOneid(IdUtil.fastSimpleUUID());
            user.setOpenid(wxResult.getOpenid());
            user.setUnionId(wxResult.getUnionid());
            user.setSubscribeStatus(1); // 已关注
            user.setSubscribeTime(LocalDateTime.now());
            // 优先使用授权手机号，如果没有则使用请求中的手机号
            user.setMobile(authorizedMobile != null ? authorizedMobile : reqVO.getMobile());
            user.setNickname(reqVO.getNickname());
            // 使用上传后的头像URL
            user.setHeadImageUrl(uploadedHeadImageUrl != null ? uploadedHeadImageUrl : reqVO.getHeadImageUrl());
            user.setAppId(wxMiniProgramConfig.getAppId()); // 使用配置的微信小程序appid
            user.setAccountId(1L); // 默认账号ID
            user.setCreator("system");
            user.setUpdater("system");
            user.setTenantId(1L);

            mpUserMapper.insert(user);
            log.info("创建新用户成功，用户ID: {}, 手机号: {}", user.getId(), user.getMobile());
        } else {
            // 更新用户信息
            if (StrUtil.isNotBlank(reqVO.getNickname())) {
                user.setNickname(reqVO.getNickname());
            }
            // 如果有新的头像URL，则更新
            if (uploadedHeadImageUrl != null) {
                user.setHeadImageUrl(uploadedHeadImageUrl);
            } else if (StrUtil.isNotBlank(reqVO.getHeadImageUrl())) {
                user.setHeadImageUrl(reqVO.getHeadImageUrl());
            }
            // 如果有授权手机号，优先更新授权手机号
            if (authorizedMobile != null) {
                user.setMobile(authorizedMobile);
                log.info("更新用户授权手机号: {}", authorizedMobile);
            } else if (StrUtil.isNotBlank(reqVO.getMobile())) {
                user.setMobile(reqVO.getMobile());
            }
            user.setUpdater("system");
            user.setUpdateTime(LocalDateTime.now());

            mpUserMapper.updateById(user);
            log.info("更新用户信息成功，用户ID: {}, 手机号: {}", user.getId(), user.getMobile());
        }

        // 4. 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user, user.getMobile(), LoginLogTypeEnum.LOGIN_SOCIAL,
                wxResult.getOpenid());
    }

    @Override
    @Transactional
    public EmployerAuthLoginRespVO smsLogin(EmployerAuthSmsLoginReqVO reqVO) {
        // 1. 校验验证码
        String userIp = getClientIP();
        smsCodeApi.useSmsCode(new SmsCodeUseReqDTO()
                .setMobile(reqVO.getMobile())
                .setCode(reqVO.getCode())
                .setScene(SmsSceneEnum.MEMBER_LOGIN.getScene())
                .setUsedIp(userIp))
                .checkError();

        // 2. 处理头像上传（如果有的话）
        String uploadedHeadImageUrl = null;
        if (StrUtil.isNotBlank(reqVO.getHeadImageUrl())) {
            uploadedHeadImageUrl = uploadWeixinHeadImage(reqVO.getHeadImageUrl());
        }

        // 3. 根据手机号查找或创建用户
        MpUserDO user = mpUserMapper.selectByMobile(reqVO.getMobile());
        if (user == null) {
            // 创建新用户
            user = new MpUserDO();
            user.setOneid(IdUtil.fastSimpleUUID());
            user.setSubscribeStatus(1); // 已关注
            user.setSubscribeTime(LocalDateTime.now());
            user.setMobile(reqVO.getMobile());
            user.setNickname(reqVO.getNickname());
            // 使用上传后的头像URL
            user.setHeadImageUrl(uploadedHeadImageUrl != null ? uploadedHeadImageUrl : reqVO.getHeadImageUrl());
            user.setAppId(wxMiniProgramConfig.getAppId()); // 使用配置的微信小程序appid
            user.setAccountId(1L); // 默认账号ID
            user.setCreator("system");
            user.setUpdater("system");
            user.setTenantId(1L);

            mpUserMapper.insert(user);
        } else {
            // 更新用户信息
            if (StrUtil.isNotBlank(reqVO.getNickname())) {
                user.setNickname(reqVO.getNickname());
            }
            // 如果有新的头像URL，则更新
            if (uploadedHeadImageUrl != null) {
                user.setHeadImageUrl(uploadedHeadImageUrl);
            } else if (StrUtil.isNotBlank(reqVO.getHeadImageUrl())) {
                user.setHeadImageUrl(reqVO.getHeadImageUrl());
            }
            user.setUpdater("system");
            user.setUpdateTime(LocalDateTime.now());

            mpUserMapper.updateById(user);
        }

        // 3. 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user, user.getMobile(), LoginLogTypeEnum.LOGIN_SMS, user.getOpenid());
    }

    @Override
    public void sendSmsCode(EmployerAuthSmsSendReqVO reqVO) {
        // 发送验证码
        smsCodeApi.sendSmsCode(new SmsCodeSendReqDTO()
                .setMobile(reqVO.getMobile())
                .setScene(SmsSceneEnum.MEMBER_LOGIN.getScene())
                .setCreateIp(getClientIP()))
                .checkError();
    }

    @Override
    public void validateSmsCode(EmployerAuthSmsValidateReqVO reqVO) {
        // 校验验证码
        smsCodeApi.validateSmsCode(new SmsCodeValidateReqDTO()
                .setMobile(reqVO.getMobile())
                .setCode(reqVO.getCode())
                .setScene(SmsSceneEnum.MEMBER_LOGIN.getScene()));
    }

    @Override
    public EmployerAuthLoginRespVO refreshToken(String refreshToken) {
        // 使用刷新令牌获取新的访问令牌
        OAuth2AccessTokenRespDTO accessTokenRespDTO = oauth2TokenApi.refreshAccessToken(refreshToken,
                OAuth2ClientConstants.CLIENT_ID_DEFAULT).getCheckedData();

        // 获取用户信息
        MpUserDO user = mpUserMapper.selectById(accessTokenRespDTO.getUserId());
        if (user == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "用户不存在");
        }

        // 构建返回结果
        return buildLoginRespVO(user, accessTokenRespDTO, user.getOpenid());
    }

    /**
     * 构建登录响应VO
     */
    private EmployerAuthLoginRespVO buildLoginRespVO(MpUserDO user, OAuth2AccessTokenRespDTO accessTokenRespDTO,
            String openid) {
        EmployerAuthLoginRespVO respVO = new EmployerAuthLoginRespVO();
        respVO.setUserId(user.getId());
        respVO.setAccessToken(accessTokenRespDTO.getAccessToken());
        respVO.setRefreshToken(accessTokenRespDTO.getRefreshToken());
        respVO.setExpiresTime(accessTokenRespDTO.getExpiresTime());
        respVO.setNickname(user.getNickname());
        respVO.setHeadImageUrl(user.getHeadImageUrl());
        respVO.setMobile(user.getMobile());
        respVO.setOpenid(openid);
        respVO.setRole("employer");
        return respVO;
    }

    /**
     * 获取客户端IP
     */
    private String getClientIP() {
        return ServletUtils.getClientIP();
    }

    /**
     * 上传微信头像到服务器
     *
     * @param weixinHeadImageUrl 微信头像URL
     * @return 上传后的文件URL
     */
    private String uploadWeixinHeadImage(String weixinHeadImageUrl) {
        try {
            log.info("开始上传微信头像 - 原始URL: {}", weixinHeadImageUrl);

            // 1. 下载微信头像
            byte[] imageBytes = HttpUtil.downloadBytes(weixinHeadImageUrl);
            if (imageBytes == null || imageBytes.length == 0) {
                log.warn("下载微信头像失败，URL: {}", weixinHeadImageUrl);
                return weixinHeadImageUrl; // 返回原始URL
            }

            // 2. 生成文件名
            String fileName = "weixin_avatar_" + IdUtil.fastSimpleUUID() + ".jpg";

            // 3. 上传到服务器
            String uploadedUrl = fileUploadService.uploadImage(imageBytes, fileName, "avatar");

            log.info("微信头像上传成功 - 原始URL: {}, 上传后URL: {}", weixinHeadImageUrl, uploadedUrl);
            return uploadedUrl;

        } catch (Exception e) {
            log.error("上传微信头像失败 - URL: {}, 错误: {}", weixinHeadImageUrl, e.getMessage(), e);
            // 如果上传失败，返回原始URL
            return weixinHeadImageUrl;
        }
    }

    /**
     * 创建 Token 令牌，记录登录日志
     */
    private EmployerAuthLoginRespVO createTokenAfterLoginSuccess(MpUserDO user, String mobile,
            LoginLogTypeEnum logType, String openid) {
        // 插入登陆日志
        createLoginLog(user.getId(), mobile, logType, LoginResultEnum.SUCCESS);
        // 创建 Token 令牌
        OAuth2AccessTokenRespDTO accessTokenRespDTO = oauth2TokenApi
                .createAccessToken(new OAuth2AccessTokenCreateReqDTO()
                        .setUserId(user.getId()).setUserType(getUserType().getValue())
                        .setClientId(OAuth2ClientConstants.CLIENT_ID_DEFAULT))
                .getCheckedData();
        // 构建返回结果
        return buildLoginRespVO(user, accessTokenRespDTO, openid);
    }

    /**
     * 创建登录日志
     */
    private void createLoginLog(Long userId, String mobile, LoginLogTypeEnum logType, LoginResultEnum loginResult) {
        // 插入登录日志
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logType.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(getUserType().getValue());
        reqDTO.setUsername(mobile);
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(getClientIP());
        reqDTO.setResult(loginResult.getResult());
        loginLogApi.createLoginLog(reqDTO).checkError();
    }

    /**
     * 获取用户类型
     */
    private UserTypeEnum getUserType() {
        return UserTypeEnum.MEMBER; // 使用MEMBER类型，因为雇主端也属于会员类型
    }

    @Override
    @Transactional
    public EmployerUserUpdateRespVO updateUserInfo(EmployerUserUpdateReqVO reqVO) {
        // 1. 获取当前登录用户ID
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        if (currentUserId == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "用户未登录或登录状态已过期");
        }

        // 2. 参数验证
        if (StrUtil.isBlank(reqVO.getAvatar()) && StrUtil.isBlank(reqVO.getNickname())) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "至少需要提供一个参数（avatar或nickname）");
        }

        // 3. 查询用户信息
        MpUserDO user = mpUserMapper.selectById(currentUserId);
        if (user == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "用户不存在");
        }

        // 4. 处理头像更新
        if (StrUtil.isNotBlank(reqVO.getAvatar())) {
            // 验证头像URL格式
            if (!isValidImageUrl(reqVO.getAvatar())) {
                throw ServiceExceptionUtil.exception(BAD_REQUEST, "头像URL格式不正确");
            }
            user.setHeadImageUrl(reqVO.getAvatar());
            log.info("更新用户头像 - 用户ID: {}, 新头像: {}", currentUserId, reqVO.getAvatar());
        }

        // 5. 处理昵称更新
        if (StrUtil.isNotBlank(reqVO.getNickname())) {
            // 检查昵称是否已被其他用户使用
            MpUserDO existingUser = mpUserMapper.selectByNickname(reqVO.getNickname());
            if (existingUser != null && !existingUser.getId().equals(user.getId())) {
                throw ServiceExceptionUtil.exception(BAD_REQUEST, "昵称已被其他用户使用");
            }
            user.setNickname(reqVO.getNickname());
            log.info("更新用户昵称 - 用户ID: {}, 新昵称: {}", currentUserId, reqVO.getNickname());
        }

        // 6. 更新用户信息
        user.setUpdater("system");
        user.setUpdateTime(LocalDateTime.now());
        mpUserMapper.updateById(user);

        // 7. 构建响应数据
        EmployerUserUpdateRespVO respVO = new EmployerUserUpdateRespVO();
        respVO.setUserId(String.valueOf(currentUserId));
        respVO.setAvatar(user.getHeadImageUrl());
        respVO.setNickname(user.getNickname());
        respVO.setUpdateTime(user.getUpdateTime());

        log.info("用户信息更新成功 - 用户ID: {}", currentUserId);
        return respVO;
    }

    /**
     * 验证图片URL是否有效
     */
    private boolean isValidImageUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return false;
        }
        // 简单的URL格式验证
        return url.startsWith("http://") || url.startsWith("https://");
    }

}
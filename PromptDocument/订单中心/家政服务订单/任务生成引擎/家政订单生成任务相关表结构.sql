#  订单主表
CREATE TABLE `publicbiz_order` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
`tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
`creator` varchar(64) DEFAULT NULL COMMENT '创建人',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updater` varchar(64) DEFAULT NULL COMMENT '更新人',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
`order_no` varchar(50) NOT NULL COMMENT '订单号',
`order_type` varchar(30) NOT NULL COMMENT '订单类型：practice-高校实践/training-企业培训/personal-个人培训/domestic-家政服务/certification-考试认证',
`business_line` varchar(50) NOT NULL COMMENT '业务线：高校实践/企业培训/个人培训与认证/家政服务',
`opportunity_id` varchar(50) DEFAULT NULL COMMENT '关联商机ID',
`lead_id` varchar(50) DEFAULT NULL COMMENT '关联线索ID',
`project_name` varchar(200) DEFAULT NULL COMMENT '项目名称',
`project_description` text COMMENT '项目描述',
`start_date` date DEFAULT NULL COMMENT '开始日期',
`end_date` date DEFAULT NULL COMMENT '结束日期',
`total_amount` decimal(12,2) NOT NULL COMMENT '订单总金额',
`paid_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '已支付金额',
`refund_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '退款金额',
`payment_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '支付状态：pending-待支付/paid-已支付/refunded-已退款/cancelled-已取消',
`order_status` varchar(30) NOT NULL DEFAULT 'draft' COMMENT '订单状态：draft-草稿/pending_approval-待审批/approving-审批中/approved-已批准/rejected-已拒绝/pending_payment-待支付/executing-执行中/completed-已完成/cancelled-已取消',
`reject_reason` varchar(800) DEFAULT NULL COMMENT '拒绝原因',
`manager_id` bigint(20) DEFAULT NULL COMMENT '负责人ID',
`manager_name` varchar(50) DEFAULT NULL COMMENT '负责人姓名',
`manager_phone` varchar(20) DEFAULT NULL COMMENT '负责人电话',
`contract_type` varchar(20) DEFAULT 'electronic' COMMENT '合同类型：electronic-电子合同/paper-纸质合同',
`contract_file_url` varchar(500) DEFAULT NULL COMMENT '合同文件URL',
`contract_status` varchar(20) DEFAULT 'unsigned' COMMENT '合同状态：unsigned-未签署/signed-已签署/rejected-已拒绝',
`remark` text COMMENT '备注',
`settlement_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '结算状态：pending-待结算/processing-结算中/completed-已结算/failed-结算失败',
`settlement_time` datetime DEFAULT NULL COMMENT '结算时间',
`settlement_method` varchar(30) DEFAULT NULL COMMENT '结算方式',
`is_selected_for_reconciliation` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被选中生成对账单：0-未选中，1-已选中',
`selection_time` datetime DEFAULT NULL COMMENT '选中时间',
`selector_id` bigint(20) DEFAULT NULL COMMENT '选择人ID',
`selector_name` varchar(50) DEFAULT NULL COMMENT '选择人姓名',
PRIMARY KEY (`id`),
UNIQUE KEY `uk_order_no` (`order_no`),
KEY `idx_tenant_id` (`tenant_id`),
KEY `idx_order_type` (`order_type`),
KEY `idx_business_line` (`business_line`),
KEY `idx_payment_status` (`payment_status`),
KEY `idx_order_status` (`order_status`),
KEY `idx_settlement_status` (`settlement_status`),
KEY `idx_is_selected_for_reconciliation` (`is_selected_for_reconciliation`),
KEY `idx_manager_id` (`manager_id`),
KEY `idx_create_time` (`create_time`),
KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=3006 DEFAULT CHARSET=utf8mb4 COMMENT='订单主表';

#  家政订单详情表
CREATE TABLE `publicbiz_domestic_order` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
`tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
`creator` varchar(64) DEFAULT NULL COMMENT '创建人',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updater` varchar(64) DEFAULT NULL COMMENT '更新人',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
`order_id` bigint(20) NOT NULL COMMENT '订单ID',
`order_no` varchar(50) NOT NULL COMMENT '订单号',
`customer_oneid` varchar(50) NOT NULL DEFAULT '' COMMENT '客户OneID GUID',
`customer_name` varchar(50) NOT NULL COMMENT '客户姓名',
`customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
`customer_address` varchar(500) NOT NULL COMMENT '服务地址',
`customer_remark` varchar(2000) DEFAULT NULL COMMENT '客户备注',
`service_category_id` bigint(20) NOT NULL COMMENT '服务分类ID',
`service_category_name` varchar(255) NOT NULL COMMENT '服务分类名称',
`service_package_id` bigint(20) DEFAULT NULL COMMENT '服务套餐ID',
`service_package_name` varchar(200) DEFAULT NULL COMMENT '服务套餐名称',
`service_start_date` date DEFAULT NULL COMMENT '服务开始日期',
`service_end_date` date DEFAULT NULL COMMENT '服务结束日期',
`service_duration` varchar(50) DEFAULT NULL COMMENT '服务时长',
`service_frequency` varchar(50) DEFAULT NULL COMMENT '服务频次',
`service_package_thumbnail` varchar(500) DEFAULT NULL COMMENT '套餐主图URL',
`service_package_price` decimal(10,2) NOT NULL COMMENT '套餐价格',
`service_package_original_price` decimal(10,2) DEFAULT NULL COMMENT '套餐原价',
`service_package_unit` varchar(20) NOT NULL COMMENT '价格单位：次/项/天/月',
`service_package_duration` varchar(100) DEFAULT NULL COMMENT '服务时长，如：4小时、26天、90天',
`service_package_type` varchar(20) NOT NULL COMMENT '套餐类型：long-term-长周期套餐/count-card-次数次卡套餐',
`service_description` text COMMENT '服务描述',
`service_details` longtext COMMENT '详细服务内容，富文本格式',
`service_process` longtext COMMENT '服务流程，富文本格式',
`purchase_notice` text COMMENT '购买须知',
`service_times` int(11) DEFAULT '1' COMMENT '服务次数',
`unit_price` decimal(10,2) NOT NULL COMMENT '单价',
`total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
`discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠金额',
`actual_amount` decimal(10,2) NOT NULL COMMENT '实付金额',
`service_address` varchar(500) NOT NULL COMMENT '服务地址',
`service_address_detail` varchar(200) DEFAULT NULL COMMENT '详细地址',
`service_latitude` decimal(10,6) DEFAULT NULL COMMENT '服务地址纬度',
`service_longitude` decimal(10,6) DEFAULT NULL COMMENT '服务地址经度',
`service_schedule` json DEFAULT NULL COMMENT '服务时间安排(JSON格式)',
`practitioner_oneid` varchar(36) DEFAULT NULL COMMENT '服务人员OneID',
`practitioner_name` varchar(50) DEFAULT NULL COMMENT '服务人员姓名',
`practitioner_phone` varchar(20) DEFAULT NULL COMMENT '服务人员电话',
`agency_id` bigint(20) DEFAULT NULL COMMENT '服务机构ID',
`agency_name` varchar(200) DEFAULT NULL COMMENT '服务机构名称',
`task_count` int(11) DEFAULT '0' COMMENT '任务总数',
`completed_task_count` int(11) DEFAULT '0' COMMENT '已完成任务数',
`task_progress` decimal(5,2) DEFAULT '0.00' COMMENT '任务进度百分比',
`service_fee` decimal(10,2) DEFAULT NULL COMMENT '服务费',
`agency_fee` decimal(10,2) DEFAULT NULL COMMENT '机构费',
`platform_fee` decimal(10,2) DEFAULT NULL COMMENT '平台费',
PRIMARY KEY (`id`),
UNIQUE KEY `uk_order_id` (`order_id`),
KEY `idx_order_no` (`order_no`),
KEY `idx_customer_name` (`customer_name`),
KEY `idx_practitioner_oneId` (`practitioner_oneid`),
KEY `idx_agency_id` (`agency_id`),
KEY `idx_service_start_date` (`service_start_date`),
KEY `idx_create_time` (`create_time`),
KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=2021 DEFAULT CHARSET=utf8mb4 COMMENT='家政服务订单详情表';

# 家政服务任务表
CREATE TABLE `publicbiz_domestic_task` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
`tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
`creator` varchar(64) DEFAULT NULL COMMENT '创建人',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updater` varchar(64) DEFAULT NULL COMMENT '更新人',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
`order_id` bigint(20) NOT NULL COMMENT '订单ID',
`order_no` varchar(50) NOT NULL COMMENT '订单号',
`domestic_order_id` bigint(20) NOT NULL COMMENT '家政服务订单ID',
`task_no` varchar(50) NOT NULL COMMENT '任务编号',
`task_sequence` int(11) NOT NULL COMMENT '任务序号',
`task_name` varchar(200) NOT NULL COMMENT '任务名称',
`task_description` text COMMENT '任务描述',
`task_type` varchar(50) NOT NULL COMMENT '任务类型',
`task_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '任务状态：pending-待分配/assigned-已分配/in_progress-进行中/completed-已完成/cancelled-已取消',
`planned_start_time` datetime DEFAULT NULL COMMENT '计划开始时间',
`planned_end_time` datetime DEFAULT NULL COMMENT '计划结束时间',
`actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
`actual_end_time` datetime DEFAULT NULL COMMENT '实际结束时间',
`duration` varchar(50) DEFAULT NULL COMMENT '任务时长',
`practitioner_oneid` varchar(36) DEFAULT NULL COMMENT '服务人员OneID',
`practitioner_name` varchar(50) DEFAULT NULL COMMENT '服务人员姓名',
`practitioner_phone` varchar(20) DEFAULT NULL COMMENT '服务人员电话',
`schedule_date` date NOT NULL COMMENT '排班日期',
`service_category_id` bigint(20) NOT NULL COMMENT '服务分类ID',
`service_category_name` varchar(255) NOT NULL COMMENT '服务分类名称',
`customer_id` bigint(20) NOT NULL COMMENT '客户ID',
`customer_name` varchar(64) NOT NULL COMMENT '客户姓名',
`customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
`service_address` varchar(255) NOT NULL COMMENT '服务地址',
`punch_in_time` datetime DEFAULT NULL COMMENT '打卡开始时间',
`punch_out_time` datetime DEFAULT NULL COMMENT '打卡结束时间',
`punch_location` varchar(255) DEFAULT NULL COMMENT '打卡位置',
`punch_latitude` decimal(10,7) DEFAULT NULL COMMENT '打卡纬度',
`punch_longitude` decimal(10,7) DEFAULT NULL COMMENT '打卡经度',
PRIMARY KEY (`id`),
UNIQUE KEY `uk_task_no` (`task_no`),
KEY `idx_order_id` (`order_id`),
KEY `idx_order_no` (`order_no`),
KEY `idx_domestic_order_id` (`domestic_order_id`),
KEY `idx_task_sequence` (`task_sequence`),
KEY `idx_task_status` (`task_status`),
KEY `idx_practitioner_oneId` (`practitioner_oneid`),
KEY `idx_planned_start_time` (`planned_start_time`),
KEY `idx_create_time` (`create_time`),
KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COMMENT='家政服务任务表';

CREATE TABLE `publicbiz_service_package` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
`tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
`creator` varchar(64) DEFAULT NULL COMMENT '创建人',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updater` varchar(64) DEFAULT NULL COMMENT '更新人',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
`name` varchar(200) NOT NULL COMMENT '套餐名称',
`category` varchar(50) NOT NULL COMMENT '服务分类：日常保洁/深度保洁/家电清洗/专项服务/月嫂服务/收纳整理',
`thumbnail` varchar(500) DEFAULT NULL COMMENT '套餐主图URL',
`price` decimal(10,2) NOT NULL COMMENT '套餐价格',
`original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
`unit` varchar(20) NOT NULL COMMENT '价格单位：次/项/天/月',
`service_duration` varchar(100) DEFAULT NULL COMMENT '服务时长，如：4小时、26天、90天',
`package_type` varchar(20) NOT NULL COMMENT '套餐类型：long-term-长周期套餐/count-card-次数次卡套餐',
`task_split_rule` varchar(200) DEFAULT NULL COMMENT '任务拆分规则',
`service_description` text COMMENT '服务描述，建议100-200字',
`service_details` longtext COMMENT '详细服务内容，富文本格式',
`service_process` longtext COMMENT '服务流程，富文本格式',
`purchase_notice` text COMMENT '购买须知',
`status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态：active-已上架/pending-待上架/deleted-回收站',
`advance_booking_days` int(11) NOT NULL DEFAULT '1' COMMENT '预约时间范围：1-提前1天/3-提前3天/7-提前7天',
`time_selection_mode` varchar(20) NOT NULL DEFAULT 'fixed' COMMENT '时间选择模式：fixed-固定时间/flexible-灵活时间',
`appointment_mode` varchar(20) NOT NULL DEFAULT 'start-date' COMMENT '预约模式：start-date-开始日期预约/all-at-once-一次性预约全部服务次数',
`service_start_time` varchar(20) NOT NULL DEFAULT 'within-3-days' COMMENT '服务开始时间：within-3-days-下单后3天内开始/specified-date-指定日期开始',
`address_setting` varchar(20) NOT NULL DEFAULT 'fixed' COMMENT '地址设置：fixed-固定地址/changeable-可变更地址',
`max_booking_days` int(11) NOT NULL DEFAULT '30' COMMENT '最大预约天数',
`cancellation_policy` varchar(500) DEFAULT NULL COMMENT '取消政策',
`audit_status` varchar(20) NOT NULL DEFAULT 'auditing' COMMENT '审核状态：pending-待审核， auditing-审核中，approved-已通过，rejected-已拒绝',
`agency_id` bigint(20) DEFAULT NULL COMMENT '所属机构ID',
`agency_name` varchar(100) DEFAULT NULL COMMENT '所属机构名称',
`reject_reason` text COMMENT '拒绝原因，审核拒绝时填写',
`category_id` bigint(20) DEFAULT NULL COMMENT '服务分类ID',
`service_time_start` varchar(50) DEFAULT NULL COMMENT '服务开始时间，如：09:00:00',
`service_time_end` varchar(50) DEFAULT NULL COMMENT '服务结束时间，如：13:00:00',
`rest_day_type` varchar(20) DEFAULT NULL COMMENT '休息日类型：none-无特殊设置/sunday-周日/statutory-法定节假日/both-周日及法定节假日/negotiable-客户协商',
`service_timespan` varchar(4000) DEFAULT NULL COMMENT '服务时间，如：9:00-13:00、全天、夜班',
`service_times` int(11) DEFAULT NULL COMMENT '服务次数(次)或服务周期(天)，如：4、6、10',
`validity_period` int(11) DEFAULT NULL COMMENT '有效期（天），如：90天、180天、365天',
`validity_period_unit` varchar(20) DEFAULT 'day' COMMENT '有效期单位：day-天/week-周/month-月/year-年',
`service_interval_type` varchar(20) DEFAULT NULL COMMENT '服务间隔类型/服务频次类型：day-每天/weekly-每周/monthly-每月/year-每年',
`service_interval_value` int(11) DEFAULT NULL COMMENT '服务间隔数值/服务频次数值，如：1表示每周1次或每月1次',
`single_duration_hours` int(11) DEFAULT NULL COMMENT '单次服务时长（小时），如：2、4、6',
PRIMARY KEY (`id`),
KEY `idx_tenant_id` (`tenant_id`),
KEY `idx_category` (`category`),
KEY `idx_status` (`status`),
KEY `idx_package_type` (`package_type`),
KEY `idx_create_time` (`create_time`),
KEY `idx_deleted` (`deleted`),
KEY `idx_agency_id` (`agency_id`),
KEY `idx_audit_status` (`audit_status`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COMMENT='服务套餐主表';



-- 订单支付记录表
CREATE TABLE `publicbiz_order_payment` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 业务字段
  `order_id` BIGINT NOT NULL COMMENT '订单ID',
  `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
  `payment_no` VARCHAR(50) NOT NULL COMMENT '支付单号',
  `payment_type` VARCHAR(20) NOT NULL COMMENT '支付类型：cash-现金/wechat-微信支付/alipay-支付宝/bank_transfer-银行转账/pos-POS机刷卡/other-其他',
  `payment_amount` DECIMAL(12,2) NOT NULL COMMENT '支付金额',
  `payment_status` VARCHAR(20) NOT NULL COMMENT '支付状态：pending-待支付/success-支付成功/failed-支付失败/cancelled-已取消',
  `payment_time` DATETIME COMMENT '支付时间',
  `operator_id` BIGINT COMMENT '操作人ID',
  `operator_name` VARCHAR(50) COMMENT '操作人姓名',
  `payment_remark` TEXT COMMENT '支付备注',
  `transaction_id` VARCHAR(100) COMMENT '第三方交易号',
  
  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payment_no` (`payment_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_payment_type` (`payment_type`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_payment_time` (`payment_time`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='订单支付记录表';
 


-- 订单中心处理日志表
CREATE TABLE `publicbiz_order_log` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 关联工单
  `order_no` VARCHAR(64) NOT NULL COMMENT '关联工单编号',
  
  -- 日志信息
  `log_type` VARCHAR(30) NOT NULL COMMENT '日志类型：订单创建/编辑/审批通过/审批驳回/确认收款/完成/系统管理员',
  `log_title` VARCHAR(200) COMMENT '日志标题',
  `log_content` TEXT COMMENT '日志内容',
	`old_status` VARCHAR(50) NOT NULL COMMENT '原状态',
  `new_status` VARCHAR(50) NOT NULL COMMENT '新状态',

  -- 操作人信息
  `operator_id` BIGINT COMMENT '操作人ID',
  `operator_name` VARCHAR(50) COMMENT '操作人姓名',
  `operator_role` VARCHAR(50) COMMENT '操作人角色',
  
  -- 关联方信息（用于显示如"雇主: 王先生"）
  `related_party_type` VARCHAR(30) COMMENT '关联方类型',
  `related_party_name` VARCHAR(100) COMMENT '关联方名称',
  
  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_work_order_no` (`order_no`),
  KEY `idx_log_type` (`log_type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='订单中心处理日志表';
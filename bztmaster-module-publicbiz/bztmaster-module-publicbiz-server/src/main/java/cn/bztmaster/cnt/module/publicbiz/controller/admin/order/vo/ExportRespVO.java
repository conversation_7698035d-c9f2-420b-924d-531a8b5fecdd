package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 导出响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 导出响应 VO")
@Data
public class ExportRespVO {

    @Schema(description = "下载URL", example = "https://example.com/download/orders_123456789.xlsx")
    private String downloadUrl;

    @Schema(description = "文件名", example = "个人培训订单_20240601.xlsx")
    private String fileName;
}




package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 个人培训与认证订单导出请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 个人培训与认证订单导出请求")
@Data
public class PersonalTrainingExportReqVO {

    @Schema(description = "订单状态筛选", example = "pending_payment")
    private String orderStatus;

    @Schema(description = "支付状态筛选", example = "pending")
    private String paymentStatus;

    @Schema(description = "订单类型筛选", example = "training")
    private String orderType;

    @Schema(description = "关键词搜索", example = "王小明")
    private String keyword;

    @Schema(description = "开始日期", example = "2024-06-01")
    private String startDate;

    @Schema(description = "结束日期", example = "2024-06-20")
    private String endDate;

    @Schema(description = "导出类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "excel")
    @NotNull(message = "导出类型不能为空")
    private String exportType;
}


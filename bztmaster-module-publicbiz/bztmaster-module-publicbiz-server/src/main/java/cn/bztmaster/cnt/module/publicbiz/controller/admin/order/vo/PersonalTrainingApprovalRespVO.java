package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 个人培训与认证订单审批响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 个人培训与认证订单审批响应")
@Data
public class PersonalTrainingApprovalRespVO {

    @Schema(description = "审批ID", example = "1")
    private Long approvalId;

    @Schema(description = "审批单号", example = "APV202406200001")
    private String approvalNo;

    @Schema(description = "订单ID", example = "1")
    private Long orderId;

    @Schema(description = "审批类型", example = "order_approval")
    private String approvalType;

    @Schema(description = "审批级别", example = "1")
    private Integer approvalLevel;

    @Schema(description = "审批结果", example = "approved")
    private String approvalResult;

    @Schema(description = "审批意见", example = "同意此订单")
    private String approvalOpinion;

    @Schema(description = "审批人姓名", example = "张三")
    private String approverName;

    @Schema(description = "审批时间", example = "2024-06-20 10:00:00")
    private String approvalTime;

    @Schema(description = "创建时间", example = "2024-06-20 10:00:00")
    private String createTime;

    @Schema(description = "更新时间", example = "2024-06-20 10:00:00")
    private String updateTime;
}


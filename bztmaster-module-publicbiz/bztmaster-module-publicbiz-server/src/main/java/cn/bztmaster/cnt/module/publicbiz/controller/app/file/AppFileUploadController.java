package cn.bztmaster.cnt.module.publicbiz.controller.app.file;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.file.vo.FileUploadRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.file.FileUploadService;
import cn.hutool.core.io.IoUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 App - 文件上传")
@RestController
@RequestMapping("/publicbiz/file")
@Validated
@Slf4j
public class AppFileUploadController {

    @Resource
    private FileUploadService fileUploadService;

    @PostMapping("/upload")
    @Operation(summary = "上传通用文件")
    @PermitAll
    public CommonResult<FileUploadRespVO> uploadFile(
            @Parameter(description = "文件", required = true) @RequestParam("file") @NotNull MultipartFile file,
            @Parameter(description = "存储目录") @RequestParam(value = "directory", required = false) String directory) {

        try {
            byte[] fileBytes = IoUtil.readBytes(file.getInputStream());
            String fileName = file.getOriginalFilename();

            String fileUrl = fileUploadService.uploadFile(fileBytes, fileName, directory);

            FileUploadRespVO respVO = new FileUploadRespVO();
            respVO.setFileName(fileName);
            respVO.setFileUrl(fileUrl);
            respVO.setFileSize((long) fileBytes.length);
            respVO.setDirectory(directory);

            return success(respVO);

        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/upload-image")
    @Operation(summary = "上传图片文件")
    @PermitAll
    public CommonResult<FileUploadRespVO> uploadImage(
            @Parameter(description = "图片文件", required = true) @RequestParam("file") @NotNull MultipartFile file,
            @Parameter(description = "存储目录") @RequestParam(value = "directory", required = false) String directory) {

        try {
            byte[] fileBytes = IoUtil.readBytes(file.getInputStream());
            String fileName = file.getOriginalFilename();

            String fileUrl = fileUploadService.uploadImage(fileBytes, fileName, directory);

            FileUploadRespVO respVO = new FileUploadRespVO();
            respVO.setFileName(fileName);
            respVO.setFileUrl(fileUrl);
            respVO.setFileSize((long) fileBytes.length);
            respVO.setDirectory(directory);

            return success(respVO);

        } catch (Exception e) {
            log.error("图片上传失败", e);
            throw new RuntimeException("图片上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/upload-document")
    @Operation(summary = "上传文档文件")
    @PermitAll
    public CommonResult<FileUploadRespVO> uploadDocument(
            @Parameter(description = "文档文件", required = true) @RequestParam("file") @NotNull MultipartFile file,
            @Parameter(description = "存储目录") @RequestParam(value = "directory", required = false) String directory) {

        try {
            byte[] fileBytes = IoUtil.readBytes(file.getInputStream());
            String fileName = file.getOriginalFilename();

            String fileUrl = fileUploadService.uploadDocument(fileBytes, fileName, directory);

            FileUploadRespVO respVO = new FileUploadRespVO();
            respVO.setFileName(fileName);
            respVO.setFileUrl(fileUrl);
            respVO.setFileSize((long) fileBytes.length);
            respVO.setDirectory(directory);

            return success(respVO);

        } catch (Exception e) {
            log.error("文档上传失败", e);
            throw new RuntimeException("文档上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/upload-certificate")
    @Operation(summary = "上传证书文件")
    @PermitAll
    public CommonResult<FileUploadRespVO> uploadCertificate(
            @Parameter(description = "证书文件", required = true) @RequestParam("file") @NotNull MultipartFile file) {

        try {
            byte[] fileBytes = IoUtil.readBytes(file.getInputStream());
            String fileName = file.getOriginalFilename();

            String fileUrl = fileUploadService.uploadCertificate(fileBytes, fileName);

            FileUploadRespVO respVO = new FileUploadRespVO();
            respVO.setFileName(fileName);
            respVO.setFileUrl(fileUrl);
            respVO.setFileSize((long) fileBytes.length);
            respVO.setDirectory("certificates");

            return success(respVO);

        } catch (Exception e) {
            log.error("证书上传失败", e);
            throw new RuntimeException("证书上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/upload-contract")
    @Operation(summary = "上传合同文件")
    @PermitAll
    public CommonResult<FileUploadRespVO> uploadContract(
            @Parameter(description = "合同文件", required = true) @RequestParam("file") @NotNull MultipartFile file) {

        try {
            byte[] fileBytes = IoUtil.readBytes(file.getInputStream());
            String fileName = file.getOriginalFilename();

            String fileUrl = fileUploadService.uploadContract(fileBytes, fileName);

            FileUploadRespVO respVO = new FileUploadRespVO();
            respVO.setFileName(fileName);
            respVO.setFileUrl(fileUrl);
            respVO.setFileSize((long) fileBytes.length);
            respVO.setDirectory("contracts");

            return success(respVO);

        } catch (Exception e) {
            log.error("合同上传失败", e);
            throw new RuntimeException("合同上传失败: " + e.getMessage());
        }
    }
}

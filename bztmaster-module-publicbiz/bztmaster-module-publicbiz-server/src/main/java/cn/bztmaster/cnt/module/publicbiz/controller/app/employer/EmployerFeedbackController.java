package cn.bztmaster.cnt.module.publicbiz.controller.app.employer;

import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.FeedbackCreateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.FeedbackCreateRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.FeedbackDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.FeedbackListRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.employer.EmployerFeedbackService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 雇主端意见反馈")
@RestController
@RequestMapping("/publicbiz/employer/feedback")
@Validated
@Slf4j
public class EmployerFeedbackController {

    @Resource
    private EmployerFeedbackService employerFeedbackService;

    @PostMapping("/create")
    @Operation(summary = "新增意见反馈")
    public CommonResult<FeedbackCreateRespVO> createFeedback(@Valid @RequestBody FeedbackCreateReqVO createReqVO) {
        log.info("新增意见反馈 - 请求参数: {}", createReqVO);
        try {
            FeedbackCreateRespVO result = employerFeedbackService.createFeedback(createReqVO);
            return success(result);
        } catch (ServiceException e) {
            log.error("新增意见反馈失败: {}", e.getMessage());
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("新增意见反馈失败 - 参数: {}, 错误: {}", createReqVO, e.getMessage(), e);
            return CommonResult.error(500, "新增意见反馈失败，请稍后重试");
        }
    }

    @GetMapping("/list")
    @Operation(summary = "获取意见反馈列表")
    public CommonResult<FeedbackListRespVO> getFeedbackList(
            @Parameter(description = "页码，默认1", example = "1") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @Parameter(description = "每页数量，默认10", example = "10") @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @Parameter(description = "状态筛选：all/pending/processing/processed", example = "all") @RequestParam(value = "status", required = false, defaultValue = "all") String status) {
        log.info("获取意见反馈列表 - page: {}, size: {}, status: {}", page, size, status);
        try {
            FeedbackListRespVO result = employerFeedbackService.getFeedbackList(page, size, status);
            return success(result);
        } catch (ServiceException e) {
            log.error("获取意见反馈列表失败: {}", e.getMessage());
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("获取意见反馈列表失败 - 错误: {}", e.getMessage(), e);
            return CommonResult.error(500, "获取意见反馈列表失败，请稍后重试");
        }
    }

    @GetMapping("/detail/{feedbackId}")
    @Operation(summary = "获取意见反馈详情")
    public CommonResult<FeedbackDetailRespVO> getFeedbackDetail(
            @Parameter(description = "反馈ID", example = "1001") @PathVariable("feedbackId") Long feedbackId) {
        log.info("获取意见反馈详情 - feedbackId: {}", feedbackId);
        try {
            FeedbackDetailRespVO result = employerFeedbackService.getFeedbackDetail(feedbackId);
            return success(result);
        } catch (ServiceException e) {
            log.error("获取意见反馈详情失败: {}", e.getMessage());
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("获取意见反馈详情失败 - feedbackId: {}, 错误: {}", feedbackId, e.getMessage(), e);
            return CommonResult.error(500, "获取意见反馈详情失败，请稍后重试");
        }
    }
}

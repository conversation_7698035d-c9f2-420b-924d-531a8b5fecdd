package cn.bztmaster.cnt.module.publicbiz.service.employer.impl;

import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.FeedbackCreateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.FeedbackCreateRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.FeedbackListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.FeedbackDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderAttachmentDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.workorder.WorkOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.workorder.WorkOrderAttachmentMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.DomesticOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.MpUserMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic.DomesticTaskMapper;
import cn.bztmaster.cnt.module.publicbiz.service.employer.EmployerFeedbackService;
import cn.bztmaster.cnt.module.publicbiz.service.file.FileUploadService;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 雇主端意见反馈 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class EmployerFeedbackServiceImpl implements EmployerFeedbackService {

    public static final String PENDING = "pending";
    @Resource
    private WorkOrderMapper workOrderMapper;

    @Resource
    private WorkOrderAttachmentMapper workOrderAttachmentMapper;

    @Resource
    private PublicbizOrderMapper orderMapper;

    @Resource
    private DomesticOrderMapper domesticOrderMapper;

    @Resource
    private MpUserMapper mpUserMapper;

    @Resource
    private DomesticTaskMapper domesticTaskMapper;

    @Resource
    private FileUploadService fileUploadService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FeedbackCreateRespVO createFeedback(FeedbackCreateReqVO createReqVO) {
        log.info("创建意见反馈 - createReqVO: {}", createReqVO);

        try {
            // 1. 获取当前登录用户信息
            String currentUserId = getCurrentUserId();
            MpUserDO currentUser = null;

            if (currentUserId != null) {
                // 优先使用当前登录用户ID
                currentUser = mpUserMapper.selectById(Long.valueOf(currentUserId));
            }

            if (currentUser == null) {
                // 如果获取不到当前用户，则通过订单ID查找订单创建者
                log.info("无法获取当前登录用户，尝试通过订单ID查找用户信息 - orderId: {}", createReqVO.getOrderId());
                currentUser = getUserByOrderId(createReqVO.getOrderId());
            }

            if (currentUser == null) {
                throw new ServiceException(404, "用户信息不存在");
            }

            // 2. 验证订单信息
            PublicbizOrderDO orderDO = validateOrderForFeedback(createReqVO.getOrderId(),
                    currentUser.getId().toString());

            // 3. 检查是否已提交过反馈
            checkExistingFeedback(createReqVO.getOrderId());

            // 4. 处理投诉图片上传
            List<String> complaintImageUrls = processComplaintImages(createReqVO.getComplaintImages());

            // 5. 创建工单
            WorkOrderDO workOrderDO = createWorkOrder(createReqVO, currentUser, orderDO, complaintImageUrls);

            // 6. 保存附件
            saveWorkOrderAttachments(workOrderDO.getWorkOrderNo(), complaintImageUrls);

            // 7. 构建响应结果
            FeedbackCreateRespVO result = new FeedbackCreateRespVO();
            result.setFeedbackId(workOrderDO.getId());
            result.setOrderId(createReqVO.getOrderId());
            result.setCreateTime(
                    workOrderDO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            log.info("意见反馈创建成功 - feedbackId: {}, orderId: {}", workOrderDO.getId(), createReqVO.getOrderId());
            return result;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建意见反馈失败 - createReqVO: {}, 错误: {}", createReqVO, e.getMessage(), e);
            throw new ServiceException(500, "创建意见反馈失败，请稍后重试");
        }
    }

    @Override
    public FeedbackListRespVO getFeedbackList(Integer page, Integer size, String status) {
        log.info("获取意见反馈列表 - page: {}, size: {}, status: {}", page, size, status);

        try {
            // 1. 获取当前登录用户信息
            String currentUserId = getCurrentUserId();
            if (currentUserId == null) {
                throw new ServiceException(401, "用户未登录或登录状态已过期");
            }

            // 2. 设置默认分页参数
            page = page == null || page <= 0 ? 1 : page;
            size = size == null || size <= 0 ? 10 : Math.min(size, 50);

            // 3. 查询用户的订单列表
            List<PublicbizOrderDO> userOrders = orderMapper.selectListByCreator(currentUserId);

            // 4. 查询这些订单的工单列表
            List<WorkOrderDO> workOrders = new ArrayList<>();
            for (PublicbizOrderDO order : userOrders) {
                List<WorkOrderDO> orderWorkOrders = workOrderMapper.selectByOrderNo(order.getOrderNo());
                workOrders.addAll(orderWorkOrders.stream()
                        .filter(wo -> "complaint".equals(wo.getWorkOrderType()))
                        .collect(Collectors.toList()));
            }

            // 5. 根据状态筛选
            if (StrUtil.isNotEmpty(status) && !"all".equals(status)) {
                workOrders = workOrders.stream()
                        .filter(wo -> status.equals(wo.getWorkOrderStatus()))
                        .collect(Collectors.toList());
            }

            // 6. 分页处理
            int total = workOrders.size();
            int startIndex = (page - 1) * size;
            int endIndex = Math.min(startIndex + size, total);
            List<WorkOrderDO> pageWorkOrders = startIndex < total ? workOrders.subList(startIndex, endIndex)
                    : new ArrayList<>();

            // 7. 构建响应结果
            List<FeedbackListRespVO.FeedbackInfo> feedbackInfoList = new ArrayList<>();
            for (WorkOrderDO wo : pageWorkOrders) {
                feedbackInfoList.add(buildFeedbackInfo(wo));
            }

            FeedbackListRespVO result = new FeedbackListRespVO();
            result.setTotal((long) total);
            result.setList(feedbackInfoList);

            log.info("获取意见反馈列表成功 - 总数: {}, 当前页: {}", total, page);
            return result;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取意见反馈列表失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException(500, "获取意见反馈列表失败，请稍后重试");
        }
    }

    @Override
    public FeedbackDetailRespVO getFeedbackDetail(Long feedbackId) {
        log.info("获取意见反馈详情 - feedbackId: {}", feedbackId);

        try {
            // 1. 获取当前登录用户信息
            String currentUserId = getCurrentUserId();
            if (currentUserId == null) {
                throw new ServiceException(401, "用户未登录或登录状态已过期");
            }

            // 2. 查询工单信息
            WorkOrderDO workOrderDO = workOrderMapper.selectById(feedbackId);
            if (workOrderDO == null) {
                throw new ServiceException(404, "反馈不存在");
            }

            // 3. 验证权限（只能查看自己的反馈）
            validateFeedbackPermission(workOrderDO, currentUserId);

            // 4. 查询订单信息
            PublicbizOrderDO orderDO = orderMapper.selectByOrderNo(workOrderDO.getOrderNo());
            DomesticOrderDO domesticOrderDO = domesticOrderMapper.selectByOrderId(orderDO.getId());

            // 5. 查询附件信息
            List<WorkOrderAttachmentDO> attachments = workOrderAttachmentMapper
                    .selectByWorkOrderNo(workOrderDO.getWorkOrderNo());

            // 6. 构建响应结果
            FeedbackDetailRespVO result = buildFeedbackDetailResponse(workOrderDO, orderDO, domesticOrderDO,
                    attachments);

            log.info("获取意见反馈详情成功 - feedbackId: {}", feedbackId);
            return result;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取意见反馈详情失败 - feedbackId: {}, 错误: {}", feedbackId, e.getMessage(), e);
            throw new ServiceException(500, "获取意见反馈详情失败，请稍后重试");
        }
    }

    /**
     * 获取当前登录用户ID
     */
    private String getCurrentUserId() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        return userId != null ? userId.toString() : null;
    }

    /**
     * 通过订单ID获取用户信息
     */
    private MpUserDO getUserByOrderId(String orderId) {
        try {
            // 1. 通过订单ID查询订单信息
            PublicbizOrderDO orderDO = orderMapper.selectByOrderNo(orderId);
            if (orderDO == null) {
                log.warn("订单不存在 - orderId: {}", orderId);
                return null;
            }

            // 2. 获取订单创建者ID
            String creatorId = orderDO.getCreator();
            if (StrUtil.isEmpty(creatorId)) {
                log.warn("订单创建者ID为空 - orderId: {}", orderId);
                return null;
            }

            // 3. 通过创建者ID查询用户信息
            MpUserDO user = mpUserMapper.selectById(Long.valueOf(creatorId));
            if (user == null) {
                log.warn("用户信息不存在 - creatorId: {}", creatorId);
                return null;
            }

            log.info("通过订单ID成功获取用户信息 - orderId: {}, userId: {}, openid: {}",
                    orderId, user.getId(), user.getOpenid());
            return user;

        } catch (Exception e) {
            log.error("通过订单ID获取用户信息失败 - orderId: {}, 错误: {}", orderId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 通过订单ID获取阿姨信息
     */
    private AuntInfo getAuntInfoByOrderId(Long orderId) {
        try {
            // 1) 优先：正在进行中的任务 in_progress
            try {
                DomesticTaskDO inProgress = domesticTaskMapper.selectInProgressTaskByOrderId(orderId);
                if (inProgress != null && StrUtil.isNotBlank(inProgress.getPractitionerOneid())) {
                    log.info("通过进行中任务获取阿姨信息 - orderId: {}, auntOneId: {}, auntName: {}",
                            orderId, inProgress.getPractitionerOneid(), inProgress.getPractitionerName());
                    return new AuntInfo(inProgress.getPractitionerOneid(), inProgress.getPractitionerName());
                }
            } catch (Exception ignored) {
            }

            // 2) 其次：已指派任务 assigned（从后往前找最近的）
            try {
                List<DomesticTaskDO> tasks = domesticTaskMapper.selectByOrderIdOrderByTaskSequence(orderId);
                if (tasks != null && !tasks.isEmpty()) {
                    for (int i = tasks.size() - 1; i >= 0; i--) {
                        DomesticTaskDO t = tasks.get(i);
                        if (t != null && "assigned".equalsIgnoreCase(String.valueOf(t.getTaskStatus()))
                                && StrUtil.isNotBlank(t.getPractitionerOneid())) {
                            log.info("通过已指派任务获取阿姨信息 - orderId: {}, auntOneId: {}, auntName: {}",
                                    orderId, t.getPractitionerOneid(), t.getPractitionerName());
                            return new AuntInfo(t.getPractitionerOneid(), t.getPractitionerName());
                        }
                    }
                }
            } catch (Exception ignored) {
            }

            // 3) 最后：回退到订单明细上的阿姨信息
            DomesticOrderDO domesticOrderDO = domesticOrderMapper.selectByOrderId(orderId);
            if (domesticOrderDO != null && StrUtil.isNotBlank(domesticOrderDO.getPractitionerOneid())) {
                log.info("通过订单明细获取阿姨信息 - orderId: {}, auntOneId: {}, auntName: {}",
                        orderId, domesticOrderDO.getPractitionerOneid(), domesticOrderDO.getPractitionerName());
                return new AuntInfo(domesticOrderDO.getPractitionerOneid(), domesticOrderDO.getPractitionerName());
            }

            log.warn("未能获取阿姨信息 - orderId: {}", orderId);
            return null;

        } catch (Exception e) {
            log.error("通过订单ID获取阿姨信息失败 - orderId: {}, 错误: {}", orderId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 阿姨信息内部类
     */
    private static class AuntInfo {
        private final String oneId;
        private final String name;

        public AuntInfo(String oneId, String name) {
            this.oneId = oneId;
            this.name = name;
        }

        public String getOneId() {
            return oneId;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 验证订单信息
     */
    private PublicbizOrderDO validateOrderForFeedback(String orderId, String currentUserId) {
        // 1. 查询订单主表信息
        PublicbizOrderDO orderDO = orderMapper.selectByOrderNo(orderId);
        if (orderDO == null) {
            throw new ServiceException(404, "订单不存在");
        }

        // 4. 验证订单类型
        if (!"domestic".equals(orderDO.getOrderType())) {
            throw new ServiceException(400, "订单类型不正确");
        }

        return orderDO;
    }

    /**
     * 检查是否已提交过反馈
     */
    private void checkExistingFeedback(String orderNo) {
        // 根据订单号获取主表与明细，准备查询当前执行中的阿姨oneid
        PublicbizOrderDO order = orderMapper.selectByOrderNo(orderNo);
        if (order == null) {
            throw new ServiceException(404, "订单不存在");
        }

        DomesticOrderDO domesticOrder = domesticOrderMapper.selectByOrderId(order.getId());

        // 1) 优先取进行中任务的阿姨（in_progress）
        String currentAuntOneId = null;
        try {
            DomesticTaskDO inProgress = domesticTaskMapper.selectInProgressTaskByOrderId(order.getId());
            if (inProgress != null && StrUtil.isNotBlank(inProgress.getPractitionerOneid())) {
                currentAuntOneId = inProgress.getPractitionerOneid();
            }
        } catch (Exception ignore) {
        }

        // 2) 其次取已指派任务（assigned）
        if (StrUtil.isBlank(currentAuntOneId)) {
            try {
                List<DomesticTaskDO> tasks = domesticTaskMapper.selectByOrderIdOrderByTaskSequence(order.getId());
                if (tasks != null && !tasks.isEmpty()) {
                    for (int i = tasks.size() - 1; i >= 0; i--) {
                        DomesticTaskDO t = tasks.get(i);
                        if (t != null && "assigned".equalsIgnoreCase(String.valueOf(t.getTaskStatus()))
                                && StrUtil.isNotBlank(t.getPractitionerOneid())) {
                            currentAuntOneId = t.getPractitionerOneid();
                            break;
                        }
                    }
                }
            } catch (Exception ignore) {
            }
        }

        // 3) 最后回退到订单明细上的阿姨oneid
        if (StrUtil.isBlank(currentAuntOneId) && domesticOrder != null) {
            currentAuntOneId = domesticOrder.getPractitionerOneid();
        }

        // 如果仍然为空，则无法精确限定阿姨，允许提交
        if (StrUtil.isBlank(currentAuntOneId)) {
            return;
        }

        // 按照规则过滤工单：同一订单、类型=complaint、未删除、审批状态=0、同一阿姨
        List<WorkOrderDO> workOrders = workOrderMapper.selectByOrderNo(orderNo);
        String finalCurrentAuntOneId = currentAuntOneId;
        boolean exists = workOrders != null && workOrders.stream().anyMatch(wo -> wo != null
                && "complaint".equals(wo.getWorkOrderType())
                && !isDeletedTrue(wo)
                && isStatusPending(wo)
                && StrUtil.equals(finalCurrentAuntOneId, wo.getAuntOneid()));

        if (exists) {
            throw new ServiceException(409, "该订单当前阿姨已存在待处理的投诉，请等待处理");
        }
    }

    private boolean isDeletedTrue(WorkOrderDO wo) {
        try {
            Object deleted = wo.getDeleted();
            if (deleted instanceof Boolean) {
                return (Boolean) deleted;
            }
            if (deleted instanceof Number) {
                return ((Number) deleted).intValue() != 0;
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isStatusPending(WorkOrderDO wo) {
        try {
            Object status = wo.getStatus();
            if (status instanceof Number) {
                return ((Number) status).intValue() == 0;
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 处理投诉图片上传
     */
    private List<String> processComplaintImages(List<String> complaintImages) {
        List<String> imageUrls = new ArrayList<>();

        if (complaintImages != null && !complaintImages.isEmpty()) {
            log.info("开始处理投诉图片，原始图片列表: {}", complaintImages);

            for (int i = 0; i < complaintImages.size(); i++) {
                String imageData = complaintImages.get(i);
                try {
                    if (imageData != null && !imageData.trim().isEmpty()) {
                        String trimmedData = imageData.trim();

                        // base64 图片：上传后返回URL
                        if (isBase64Image(trimmedData)) {
                            String imageUrl = uploadBase64Image(trimmedData, "feedback_images");
                            if (imageUrl != null) {
                                imageUrls.add(imageUrl);
                                log.debug("base64投诉图片上传成功，URL: {}", imageUrl);
                            } else {
                                log.warn("base64投诉图片上传失败 (索引: {})", i);
                            }
                        } else if (isValidImageUrl(trimmedData)) {
                            // 有效URL：直接使用
                            imageUrls.add(trimmedData);
                            log.debug("添加有效投诉图片URL: {}", trimmedData);
                        } else {
                            log.warn("跳过无效的投诉图片格式: {} (索引: {})", imageData, i);
                        }
                    } else {
                        log.warn("跳过空或null的投诉图片数据 (索引: {})", i);
                    }
                } catch (Exception e) {
                    log.error("处理投诉图片失败 (索引: {}): {}", i, e.getMessage(), e);
                }
            }
        } else {
            log.info("投诉图片列表为空或null");
        }

        log.info("处理投诉图片完成，原始数量: {}, 有效数量: {}",
                complaintImages != null ? complaintImages.size() : 0, imageUrls.size());
        return imageUrls;
    }

    /**
     * 判断是否为base64格式的图片
     */
    private boolean isBase64Image(String data) {
        if (data == null || data.trim().isEmpty()) {
            return false;
        }
        String trimmedData = data.trim();
        if (trimmedData.startsWith("data:image/")) {
            return true;
        }
        if (trimmedData.matches("^[A-Za-z0-9+/]*={0,2}$")) {
            return trimmedData.length() > 100;
        }
        return false;
    }

    /**
     * 判断是否为有效的图片URL
     */
    private boolean isValidImageUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        String trimmedUrl = url.trim();
        return trimmedUrl.startsWith("http://") ||
                trimmedUrl.startsWith("https://") ||
                trimmedUrl.startsWith("/") ||
                trimmedUrl.startsWith("blob:") ||
                trimmedUrl.startsWith("file://");
    }

    /**
     * 上传base64图片到文件服务器
     */
    private String uploadBase64Image(String base64Data, String directory) {
        try {
            String pureBase64 = base64Data;
            if (base64Data.contains(",")) {
                pureBase64 = base64Data.substring(base64Data.indexOf(",") + 1);
            }
            byte[] imageBytes = java.util.Base64.getDecoder().decode(pureBase64);
            String fileName = "feedback_" + System.currentTimeMillis() + "_" +
                    java.util.UUID.randomUUID().toString().substring(0, 8) + ".jpg";
            String imageUrl = uploadFileToInfra(imageBytes, fileName, directory);
            log.info("base64投诉图片上传成功 - 文件名: {}, URL: {}", fileName, imageUrl);
            return imageUrl;
        } catch (Exception e) {
            log.error("上传base64投诉图片失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 上传文件到infra模块（委托给FileUploadService）
     */
    private String uploadFileToInfra(byte[] fileBytes, String fileName, String directory) {
        return fileUploadService.uploadFile(fileBytes, fileName, directory);
    }

    /**
     * 创建工单
     */
    private WorkOrderDO createWorkOrder(FeedbackCreateReqVO createReqVO, MpUserDO currentUser,
            PublicbizOrderDO orderDO, List<String> complaintImageUrls) {
        // 查询家政订单详情获取机构信息
        DomesticOrderDO domesticOrderDO = domesticOrderMapper.selectByOrderId(orderDO.getId());

        // 查询阿姨订单任务表，获取阿姨信息
        AuntInfo auntInfo = getAuntInfoByOrderId(orderDO.getId());

        WorkOrderDO workOrderDO = new WorkOrderDO();
        workOrderDO.setWorkOrderNo("WO" + IdUtil.getSnowflakeNextIdStr());
        workOrderDO.setOrderNo(orderDO.getOrderNo());
        workOrderDO.setWorkOrderTitle("订单投诉反馈");
        workOrderDO.setWorkOrderContent(createReqVO.getComplaintContent());
        workOrderDO.setWorkOrderType("complaint");
        workOrderDO.setPriority("medium");
        workOrderDO.setWorkOrderStatus(PENDING);
        workOrderDO.setComplaintType(String.join(",", createReqVO.getComplaintTypes()));
        workOrderDO.setComplaintLevel("medium");
        workOrderDO.setComplaintTime(LocalDateTime.now());
        workOrderDO.setComplainerId(Long.valueOf(currentUser.getId()));
        workOrderDO.setComplainerName(currentUser.getNickname());
        workOrderDO.setAgencyId(domesticOrderDO != null ? domesticOrderDO.getAgencyId() : null);
        workOrderDO.setAgencyName(domesticOrderDO != null ? domesticOrderDO.getAgencyName() : null);
        workOrderDO.setCreator(currentUser.getId().toString());
        workOrderDO.setUpdater(currentUser.getId().toString());
        workOrderDO.setAuntOneid(auntInfo != null ? auntInfo.getOneId() : null);
        workOrderDO.setAuntName(auntInfo != null ? auntInfo.getName() : null);
        return workOrderDO;
    }

    /**
     * 保存工单附件
     */
    private void saveWorkOrderAttachments(String workOrderNo, List<String> imageUrls) {
        if (imageUrls != null && !imageUrls.isEmpty()) {
            for (String imageUrl : imageUrls) {
                WorkOrderAttachmentDO attachment = new WorkOrderAttachmentDO();
                attachment.setWorkOrderNo(workOrderNo);
                attachment.setFileName(extractFileNameFromUrl(imageUrl));
                attachment.setFileUrl(imageUrl);
                attachment.setFileType("image");
                attachment.setFileCategory("evidence");
                attachment.setUploadPurpose("投诉证据");

                workOrderAttachmentMapper.insert(attachment);
            }
        }
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String url) {
        if (StrUtil.isEmpty(url)) {
            return "unknown.jpg";
        }
        String fileName = url.substring(url.lastIndexOf("/") + 1);
        return StrUtil.isEmpty(fileName) ? "unknown.jpg" : fileName;
    }

    /**
     * 构建反馈信息
     */
    private FeedbackListRespVO.FeedbackInfo buildFeedbackInfo(WorkOrderDO workOrderDO) {
        FeedbackListRespVO.FeedbackInfo feedbackInfo = new FeedbackListRespVO.FeedbackInfo();
        feedbackInfo.setFeedbackId(workOrderDO.getId());
        feedbackInfo.setOrderId(workOrderDO.getOrderNo());
        feedbackInfo.setComplaintTypes(parseComplaintTypes(workOrderDO.getComplaintType()));
        feedbackInfo.setComplaintContent(workOrderDO.getWorkOrderContent());
        feedbackInfo.setStatus(workOrderDO.getWorkOrderStatus());
        feedbackInfo
                .setCreateTime(workOrderDO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return feedbackInfo;
    }

    /**
     * 解析投诉类型
     */
    private List<String> parseComplaintTypes(String complaintType) {
        if (StrUtil.isEmpty(complaintType)) {
            return new ArrayList<>();
        }
        return Arrays.asList(complaintType.split(","));
    }

    /**
     * 验证反馈权限
     */
    private void validateFeedbackPermission(WorkOrderDO workOrderDO, String currentUserId) {
        // 查询订单信息
        PublicbizOrderDO orderDO = orderMapper.selectByOrderNo(workOrderDO.getOrderNo());
        if (orderDO == null) {
            throw new ServiceException(404, "订单不存在");
        }

        // 验证订单是否属于当前用户
        if (!currentUserId.equals(orderDO.getCreator())) {
            throw new ServiceException(403, "只能查看自己的反馈");
        }
    }

    /**
     * 构建反馈详情响应
     */
    private FeedbackDetailRespVO buildFeedbackDetailResponse(WorkOrderDO workOrderDO, PublicbizOrderDO orderDO,
            DomesticOrderDO domesticOrderDO, List<WorkOrderAttachmentDO> attachments) {
        FeedbackDetailRespVO result = new FeedbackDetailRespVO();
        result.setFeedbackId(workOrderDO.getId());
        result.setStatus(workOrderDO.getWorkOrderStatus());

        // 构建投诉信息
        FeedbackDetailRespVO.ComplaintInfo complaintInfo = new FeedbackDetailRespVO.ComplaintInfo();
        complaintInfo.setComplaintTypes(parseComplaintTypes(workOrderDO.getComplaintType()));
        complaintInfo.setCustomerInfo(
                workOrderDO.getComplainerName() + " " + maskPhoneNumber(getUserPhone(workOrderDO.getComplainerId())));
        complaintInfo.setOrderId(workOrderDO.getOrderNo());
        complaintInfo.setComplaintTime(
                workOrderDO.getComplaintTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        complaintInfo.setComplaintContent(workOrderDO.getWorkOrderContent());
        complaintInfo.setComplaintImages(attachments.stream()
                .map(WorkOrderAttachmentDO::getFileUrl)
                .collect(Collectors.toList()));
        result.setComplaintInfo(complaintInfo);

        // 构建服务信息
        FeedbackDetailRespVO.ServiceInfo serviceInfo = new FeedbackDetailRespVO.ServiceInfo();
        if (domesticOrderDO != null) {
            serviceInfo.setServiceType(domesticOrderDO.getServiceCategoryName());
            serviceInfo.setServiceAddress(domesticOrderDO.getServiceAddress());
            serviceInfo
                    .setServiceTime(domesticOrderDO.getServiceStartDate() + "~" + domesticOrderDO.getServiceEndDate());
            serviceInfo.setServicePersonnel(domesticOrderDO.getPractitionerName());
        }
        result.setServiceInfo(serviceInfo);

        return result;
    }

    /**
     * 获取用户手机号
     */
    private String getUserPhone(Long userId) {
        try {
            MpUserDO user = mpUserMapper.selectById(userId);
            return user != null ? user.getMobile() : "";
        } catch (Exception e) {
            log.error("获取用户手机号失败，userId: {}", userId, e);
            return "";
        }
    }

    /**
     * 掩码手机号
     */
    private String maskPhoneNumber(String phone) {
        if (StrUtil.isEmpty(phone) || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }
}

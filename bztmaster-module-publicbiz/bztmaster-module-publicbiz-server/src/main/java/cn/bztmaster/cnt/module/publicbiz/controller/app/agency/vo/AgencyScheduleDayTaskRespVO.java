package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AgencyScheduleDayTaskRespVO {
    @Schema(description = "任务ID")
    private Long id;
    @Schema(description = "任务编号")
    private String taskNo;
    @Schema(description = "服务类型")
    private String serviceType;
    @Schema(description = "日期 yyyy-MM-dd")
    private String date;
    @Schema(description = "时间段 或 时长")
    private String time;
    @Schema(description = "时长")
    private String duration;
    @Schema(description = "客户")
    private String customer;
    @Schema(description = "客户电话(脱敏)")
    private String customerPhone;
    @Schema(description = "地址")
    private String address;
    @Schema(description = "任务状态")
    private String taskStatus;
}



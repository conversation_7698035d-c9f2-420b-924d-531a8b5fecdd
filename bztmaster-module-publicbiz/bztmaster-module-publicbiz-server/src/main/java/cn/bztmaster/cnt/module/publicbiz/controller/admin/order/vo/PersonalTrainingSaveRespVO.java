package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 个人培训与认证订单保存 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 个人培训与认证订单保存 Response VO")
@Data
public class PersonalTrainingSaveRespVO {

    @Schema(description = "订单ID", example = "1")
    private Long orderId;

    @Schema(description = "订单号", example = "PT123456789")
    private String orderNo;
}




package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 个人培训订单收款请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 个人培训订单收款请求 VO")
@Data
public class PersonalTrainingCollectionReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "PT1234567890")
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @Schema(description = "收款金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "5000.00")
    @NotNull(message = "收款金额不能为空")
    @DecimalMin(value = "0.01", message = "收款金额必须大于0")
    private BigDecimal collectionAmount;

    @Schema(description = "收款方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "cash")
    @NotBlank(message = "收款方式不能为空")
    private String collectionMethod;

    @Schema(description = "收款日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-12-01")
    @NotNull(message = "收款日期不能为空")
    private LocalDate collectionDate;

    @Schema(description = "收款备注", example = "现金收款")
    private String collectionRemark;

    @Schema(description = "第三方交易号", example = "TX123456789")
    private String transactionId;

    @Schema(description = "银行账户信息", example = "6222021234567890123")
    private String bankAccount;

    @Schema(description = "银行名称", example = "中国工商银行")
    private String bankName;
}



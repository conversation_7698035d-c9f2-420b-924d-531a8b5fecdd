package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 新增意见反馈 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "新增意见反馈 Response VO")
@Data
public class FeedbackCreateRespVO {

    @Schema(description = "反馈ID", example = "1001")
    private Long feedbackId;

    @Schema(description = "订单ID", example = "TX20241219001")
    private String orderId;

    @Schema(description = "创建时间", example = "2025-02-26 14:36:00")
    private String createTime;
}

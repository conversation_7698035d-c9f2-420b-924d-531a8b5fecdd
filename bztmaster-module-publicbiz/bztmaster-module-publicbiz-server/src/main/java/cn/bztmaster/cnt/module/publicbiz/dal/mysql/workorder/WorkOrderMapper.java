package cn.bztmaster.cnt.module.publicbiz.dal.mysql.workorder;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 任务工单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WorkOrderMapper extends BaseMapperX<WorkOrderDO> {

    /**
     * 根据阿姨OneID查询工单列表
     *
     * @param auntOneId 阿姨OneID
     * @return 工单列表
     */
    List<WorkOrderDO> selectByAuntOneId(@Param("auntOneId") String auntOneId);

    /**
     * 根据阿姨OneID查询请假、调休工单列表
     *
     * @param auntOneId 阿姨OneID
     * @return 工单列表
     */
    List<WorkOrderDO> selectByAuntOneIdAndType(@Param("auntOneId") String auntOneId);

    /**
     * 根据阿姨OneID、工单类型和状态查询工单列表
     *
     * @param auntOneId 阿姨OneID
     * @param workOrderType 工单类型
     * @param status 状态
     * @return 工单列表
     */
    List<WorkOrderDO> selectByAuntOneIdAndTypeAndStatus(@Param("auntOneId") String auntOneId,
                                                        @Param("workOrderType") String workOrderType,
                                                        @Param("status") String status);

    /**
     * 根据阿姨OneID、工单类型、状态和年月查询工单列表
     *
     * @param auntOneId 阿姨OneID
     * @param workOrderType 工单类型
     * @param status 状态
     * @param year 年份
     * @param month 月份
     * @return 工单列表
     */
    List<WorkOrderDO> selectByAuntOneIdAndTypeAndStatusAndYearMonth(@Param("auntOneId") String auntOneId,
                                                                   @Param("workOrderType") String workOrderType,
                                                                   @Param("status") String status,
                                                                   @Param("year") Integer year,
                                                                   @Param("month") Integer month);

    /**
     * 根据工单编号查询工单
     *
     * @param workOrderNo 工单编号
     * @return 工单信息
     */
    WorkOrderDO selectByWorkOrderNo(@Param("workOrderNo") String workOrderNo);

    /**
     * 根据订单号查询工单列表
     *
     * @param orderNo 订单号
     * @return 工单列表
     */
    List<WorkOrderDO> selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据机构ID查询工单列表
     *
     * @param agencyId 机构ID
     * @return 工单列表
     */
    List<WorkOrderDO> selectByAgencyId(@Param("agencyId") Long agencyId);

    /**
     * 根据机构ID统计总工单数量
     *
     * @param agencyId 机构ID
     * @return 总工单数量
     */
    Long selectTotalCountByAgencyId(@Param("agencyId") Long agencyId);

    /**
     * 根据机构ID和工单类型统计工单数量
     *
     * @param agencyId 机构ID
     * @param workOrderType 工单类型
     * @return 指定类型的工单数量
     */
    Long selectCountByAgencyIdAndType(@Param("agencyId") Long agencyId, @Param("workOrderType") String workOrderType);

    /**
     * 根据机构ID和多种工单类型统计工单数量
     *
     * @param agencyId 机构ID
     * @param workOrderTypes 工单类型列表
     * @return 指定类型的工单数量
     */
    Long selectCountByAgencyIdAndTypes(@Param("agencyId") Long agencyId, @Param("workOrderTypes") List<String> workOrderTypes);

    /**
     * 检查订单是否存在审批中的换人申请
     *
     * @param orderNo 订单号
     * @return 是否存在审批中的换人申请
     */
    Boolean existsSubstitutionRequestInProgress(@Param("orderNo") String orderNo);

    /**
     * 统计阿姨指定年月范围内的请假天数
     *
     * @param auntOneId 阿姨OneID
     * @param year 年份
     * @param month 月份
     * @return 请假天数
     */
    Integer countLeaveDays(@Param("auntOneId") String auntOneId,
                           @Param("year") Integer year,
                           @Param("month") Integer month);

    /**
     * 统计阿姨指定年月范围内的调休天数
     *
     * @param auntOneId 阿姨OneID
     * @param year 年份
     * @param month 月份
     * @return 调休天数
     */
    Integer countAdjustDays(@Param("auntOneId") String auntOneId,
                            @Param("year") Integer year,
                            @Param("month") Integer month);


}

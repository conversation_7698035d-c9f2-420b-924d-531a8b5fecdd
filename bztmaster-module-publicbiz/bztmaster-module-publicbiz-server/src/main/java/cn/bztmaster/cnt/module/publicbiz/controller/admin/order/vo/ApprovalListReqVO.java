package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * 审批列表 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 审批列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApprovalListReqVO extends PageParam {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "审批状态", example = "pending")
    private String approvalStatus;

    @Schema(description = "审批类型", example = "order_approval")
    private String approvalType;
}




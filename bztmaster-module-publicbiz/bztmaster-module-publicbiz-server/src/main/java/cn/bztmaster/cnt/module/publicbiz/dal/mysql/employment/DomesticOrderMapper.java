package cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface DomesticOrderMapper extends BaseMapperX<DomesticOrderDO> {

    /**
     * 根据阿姨OneID查询订单列表
     *
     * @param practitionerOneid 阿姨OneID
     * @return 订单列表
     */
    List<DomesticOrderDO> selectByPractitionerOneid(@Param("practitionerOneid") String practitionerOneid);

    /**
     * 根据订单ID查询订单详情
     *
     * @param orderId 订单ID
     * @return 订单详情
     */
    DomesticOrderDO selectByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据客户OneID查询订单列表
     *
     * @param customerOneid 客户OneID
     * @return 订单列表
     */
    List<DomesticOrderDO> selectByCustomerOneid(@Param("customerOneid") String customerOneid);

    /**
     * 按创建时间月份分组统计订单数量
     *
     * @param agencyId  机构ID
     * @param beginTime 起始时间（含）
     * @param endTime   结束时间（含）
     * @return 列表，每条包含 month(yyyy-MM) 与 count
     */
    List<Map<String, Object>> selectCountGroupByCreateMonth(@Param("agencyId") Long agencyId,
            @Param("beginTime") Date beginTime,
            @Param("endTime") Date endTime);

    /**
     * 根据订单ID列表查询订单列表
     *
     * @param orderIds 订单ID列表
     * @return 订单列表
     */
    List<DomesticOrderDO> selectByOrderIds(@Param("orderIds") List<Long> orderIds);
}
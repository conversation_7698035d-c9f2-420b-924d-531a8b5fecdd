package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 个人培训与认证订单统计响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 个人培训与认证订单统计响应")
@Data
public class PersonalTrainingStatisticsRespVO {

    @Schema(description = "总订单数", example = "18")
    private Integer totalOrders;

    @Schema(description = "待处理订单数", example = "0")
    private Integer pendingOrders;

    @Schema(description = "本月订单金额", example = "2135220.0")
    private BigDecimal monthlyAmount;

    @Schema(description = "订单完成率", example = "92.5")
    private BigDecimal completionRate;

    @Schema(description = "个人培训订单数", example = "12")
    private Integer trainingOrderCount;

    @Schema(description = "考试认证订单数", example = "6")
    private Integer certificationOrderCount;

    @Schema(description = "已支付订单数", example = "15")
    private Integer paidOrderCount;

    @Schema(description = "待支付订单数", example = "3")
    private Integer unpaidOrderCount;
}


package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 意见反馈列表 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "意见反馈列表 Response VO")
@Data
public class FeedbackListRespVO {

    @Schema(description = "总记录数", example = "2")
    private Long total;

    @Schema(description = "反馈列表")
    private List<FeedbackInfo> list;

    @Schema(description = "反馈信息")
    @Data
    public static class FeedbackInfo {

        @Schema(description = "反馈ID", example = "1001")
        private Long feedbackId;

        @Schema(description = "订单ID", example = "TX20241219001")
        private String orderId;

        @Schema(description = "投诉类型列表", example = "[\"服务态度\", \"专业能力\"]")
        private List<String> complaintTypes;

        @Schema(description = "投诉内容", example = "阿姨在清洁过程中态度敷衍,清洁效果不理想,要求换阿姨")
        private String complaintContent;

        @Schema(description = "状态", example = "pending")
        private String status;

        @Schema(description = "创建时间", example = "2025-02-26 14:36:00")
        private String createTime;
    }
}

package cn.bztmaster.cnt.module.publicbiz.controller.app.employer;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.MpUserMapper;
import cn.hutool.core.util.StrUtil;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderCreateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderCreateRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.PackagesAndAuntInfoRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.OrderReviewReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.OrderReviewRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ReplaceRequestReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ReplaceRequestRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.InProgressOrderRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.employer.EmployerOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 雇主端订单 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "用户 APP - 雇主端订单")
@RestController
@RequestMapping("/publicbiz/employer/order")
@Validated
@Slf4j
public class EmployerOrderController {

    @Resource
    private EmployerOrderService employerOrderService;

    @Resource
    private MpUserMapper mpUserMapper;

    @GetMapping("/list")
    @Operation(summary = "获取订单列表")
    @PermitAll
    public CommonResult<EmployerOrderListRespVO> getOrderList(
            @Parameter(description = "客户OpenId", example = "12345678-1234-1234-1234-123456789012") @RequestParam("customerOpenId") String customerOpenId,
            @Parameter(description = "订单状态筛选：all-全部/pending_payment-待付款/executing-进行中/completed-已完成/cancelled-已取消", example = "all") @RequestParam(value = "status", required = false, defaultValue = "all") String status,
            @Parameter(description = "页码，默认1", example = "1") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @Parameter(description = "每页数量，默认10，最大50", example = "10") @RequestParam(value = "size", required = false, defaultValue = "10") Integer size) {

        log.info("获取雇主订单列表请求，customerOpenId: {}, status: {}, page: {}, size: {}",
                customerOpenId, status, page, size);

        try {
            // 参数验证
            if (customerOpenId == null || customerOpenId.trim().isEmpty()) {
                return CommonResult.error(400, "客户OpenId不能为空");
            }

            if (page != null && page <= 0) {
                return CommonResult.error(400, "页码必须大于0");
            }

            if (size != null && (size <= 0 || size > 50)) {
                return CommonResult.error(400, "每页数量必须在1-50之间");
            }

            // 状态验证
            if (status != null && !status.equals("all") && !status.equals("pending_payment")
                    && !status.equals("executing") && !status.equals("completed") && !status.equals("cancelled")) {
                return CommonResult.error(400, "订单状态参数无效");
            }

            EmployerOrderListRespVO result = employerOrderService.getOrderList(customerOpenId, status, page, size);
            return success(result);
        } catch (Exception e) {
            log.error("获取雇主订单列表失败 - customerOpenId: {}, 错误: {}", customerOpenId, e.getMessage(), e);
            return CommonResult.error(500, "获取订单列表失败，请稍后重试");
        }
    }

    @GetMapping("/detail")
    @Operation(summary = "获取订单详情")
    @PermitAll
    public CommonResult<EmployerOrderDetailRespVO> getOrderDetail(
            @Parameter(description = "订单ID", example = "123") @RequestParam("orderId") String orderId) {

        log.info("获取雇主订单详情请求，orderId: {}", orderId);

        try {
            // 参数验证
            if (orderId == null || orderId.trim().isEmpty()) {
                return CommonResult.error(400, "订单ID不能为空");
            }

            EmployerOrderDetailRespVO result = employerOrderService.getOrderDetail(orderId);
            return success(result);
        } catch (Exception e) {
            log.error("获取雇主订单详情失败 - orderId: {}, 错误: {}", orderId, e.getMessage(), e);
            return CommonResult.error(500, "获取订单详情失败，请稍后重试");
        }
    }

    @PostMapping("/create")
    @Operation(summary = "创建雇主订单")
    @PermitAll
    public CommonResult<EmployerOrderCreateRespVO> createOrder(
            @Valid @RequestBody EmployerOrderCreateReqVO createReqVO) {
        log.info("创建雇主订单请求，参数：{}", createReqVO);

        try {
            EmployerOrderCreateRespVO result = employerOrderService.createOrder(createReqVO);
            return success(result);
        } catch (Exception e) {
            log.error("创建雇主订单失败 - 参数: {}, 错误: {}", createReqVO, e.getMessage(), e);
            return CommonResult.error(500, "创建订单失败，请稍后重试");
        }
    }

    @GetMapping("/packages-and-aunt-info")
    @Operation(summary = "根据订单号获取套餐和阿姨信息")
    @PermitAll
    public CommonResult<PackagesAndAuntInfoRespVO> getPackagesAndAuntInfo(
            @Parameter(description = "订单ID", example = "12345") @RequestParam("orderId") Long orderId) {

        log.info("根据订单号获取套餐和阿姨信息请求，orderId: {}", orderId);

        try {
            // 参数验证
            if (orderId == null) {
                return CommonResult.error(1002, "订单ID不能为空");
            }

            PackagesAndAuntInfoRespVO result = employerOrderService.getPackagesAndAuntInfo(orderId);
            return success(result);
        } catch (Exception e) {
            log.error("根据订单号获取套餐和阿姨信息失败 - orderId: {}, 错误: {}", orderId, e.getMessage(), e);
            return CommonResult.error(5000, "获取套餐和阿姨信息失败，请稍后重试");
        }
    }

    @PostMapping("/review")
    @Operation(summary = "提交订单评价")
    public CommonResult<OrderReviewRespVO> submitOrderReview(
            @Valid @RequestBody OrderReviewReqVO reviewReqVO) {

        log.info("提交订单评价请求，参数：{}", reviewReqVO);

        try {
            OrderReviewRespVO result = employerOrderService.submitOrderReview(reviewReqVO);
            return success(result);
        } catch (ServiceException e) {
            log.error("提交订单评价失败: {}", e.getMessage());
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("提交订单评价失败 - 参数: {}, 错误: {}", reviewReqVO, e.getMessage(), e);
            return CommonResult.error(500, "提交评价失败，请稍后重试");
        }
    }

    @PostMapping("/replace")
    @Operation(summary = "申请换人")
    public CommonResult<ReplaceRequestRespVO> submitReplaceRequest(
            @Valid @RequestBody ReplaceRequestReqVO replaceReqVO) {

        log.info("申请换人请求，参数：{}", replaceReqVO);

        try {
            ReplaceRequestRespVO result = employerOrderService.submitReplaceRequest(replaceReqVO);
            return success(result);
        } catch (ServiceException e) {
            log.error("申请换人失败: {}", e.getMessage());
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("申请换人失败 - 参数: {}, 错误: {}", replaceReqVO, e.getMessage(), e);
            return CommonResult.error(500, "申请换人失败，请稍后重试");
        }
    }

    @GetMapping("/in-progress")
    @Operation(summary = "获取当前用户进行中的订单")
    @PermitAll
    public CommonResult<InProgressOrderRespVO> getInProgressOrders() {
        log.info("获取当前用户进行中的订单请求");

        try {
            // 从当前登录用户上下文获取用户OneId
            // 这里需要根据实际的用户认证机制获取当前用户OneId
            // 暂时使用占位符，实际实现时需要替换为真实的用户获取逻辑
            String currentUserOneId = getCurrentUserOneId();

            if (currentUserOneId == null || currentUserOneId.trim().isEmpty()) {
                return CommonResult.error(401, "用户未登录或登录信息无效");
            }

            InProgressOrderRespVO result = employerOrderService.getInProgressOrders(currentUserOneId);
            return success(result);
        } catch (Exception e) {
            log.error("获取进行中订单失败，错误: {}", e.getMessage(), e);
            return CommonResult.error(500, "获取进行中订单失败，请稍后重试");
        }
    }

    /**
     * 获取当前用户OneId
     */
    private String getCurrentUserOneId() {
        try {
            // 获取当前登录用户ID
            Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
            if (currentUserId == null) {
                log.warn("当前用户未登录");
                return null;
            }

            // 根据用户ID查询用户信息，获取OneId
            MpUserDO currentUser = mpUserMapper.selectById(currentUserId);
            if (currentUser == null) {
                log.warn("未找到用户信息，userId: {}", currentUserId);
                return null;
            }

            String oneId = currentUser.getOneid();
            if (StrUtil.isEmpty(oneId)) {
                log.warn("用户OneId为空，userId: {}", currentUserId);
                return null;
            }

            log.debug("获取当前用户OneId成功，userId: {}, oneId: {}", currentUserId, oneId);
            return oneId;
        } catch (Exception e) {
            log.error("获取当前用户OneId失败", e);
            return null;
        }
    }
}

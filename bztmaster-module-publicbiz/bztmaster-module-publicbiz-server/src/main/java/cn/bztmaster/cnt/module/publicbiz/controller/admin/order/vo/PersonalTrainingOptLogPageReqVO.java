package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 个人培训与认证订单操作日志分页请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 个人培训与认证订单操作日志分页请求")
@Data
public class PersonalTrainingOptLogPageReqVO {

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "PT202406001")
    @NotNull(message = "订单号不能为空")
    private String orderNo;

    @Schema(description = "日志类型筛选", example = "订单创建")
    private String logType;

    @Schema(description = "开始日期", example = "2024-06-01")
    private String startDate;

    @Schema(description = "结束日期", example = "2024-06-20")
    private String endDate;

    @Schema(description = "当前页码", example = "1")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer size = 10;
}


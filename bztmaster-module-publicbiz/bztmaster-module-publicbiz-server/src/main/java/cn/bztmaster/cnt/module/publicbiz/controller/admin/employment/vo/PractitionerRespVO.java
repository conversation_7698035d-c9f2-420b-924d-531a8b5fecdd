package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Schema(description = "就业服务-阿姨 Response VO")
public class PractitionerRespVO {
    @Schema(description = "阿姨ID")
    private Long id;

    @Schema(description = "阿姨OneID")
    private String auntOneid;

    @Schema(description = "阿姨姓名")
    private String name;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "籍贯")
    private String hometown;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "性别")
    private String gender;

    @Schema(description = "头像URL")
    private String avatar;

    @Schema(description = "主要服务类型")
    private String serviceType;

    @Schema(description = "从业年限")
    private Integer experienceYears;

    @Schema(description = "平台状态")
    private String platformStatus;

    @Schema(description = "评级")
    private BigDecimal rating;

    @Schema(description = "所属机构ID")
    private Long agencyId;

    @Schema(description = "所属机构名称")
    private String agencyName;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "当前状态")
    private String currentStatus;

    @Schema(description = "当前服务订单ID")
    private String currentOrderId;

    @Schema(description = "累计服务单数")
    private Integer totalOrders;

    @Schema(description = "累计收入")
    private BigDecimal totalIncome;

    @Schema(description = "客户满意度评分")
    private BigDecimal customerSatisfaction;

    @Schema(description = "资质文件列表")
    private List<PractitionerQualificationRespVO> qualifications;

    @Schema(description = "最近服务记录")
    private List<PractitionerServiceRecordRespVO> recentServiceRecords;

    @Schema(description = "评级历史记录")
    private List<PractitionerRatingRecordRespVO> ratingRecords;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
} 
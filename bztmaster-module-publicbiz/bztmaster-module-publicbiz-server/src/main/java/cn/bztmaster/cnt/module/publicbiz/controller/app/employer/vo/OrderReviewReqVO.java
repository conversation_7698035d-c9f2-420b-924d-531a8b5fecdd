package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 提交订单评价 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "提交订单评价 Request VO")
@Data
public class OrderReviewReqVO {

    @Schema(description = "订单ID", example = "2221")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "阿姨OneId", example = "fa2182d0-768a-11f0-ae0c-00163e1f6cb5")
    @NotBlank(message = "阿姨OneId不能为空")
    private String auntOneId;

    @Schema(description = "服务套餐ID", example = "1001")
    private Long servicePackageId;

    @Schema(description = "服务态度评分", example = "4.5")
    @NotNull(message = "服务态度评分不能为空")
    @DecimalMin(value = "1.0", message = "服务态度评分不能小于1.0")
    @DecimalMax(value = "5.0", message = "服务态度评分不能大于5.0")
    private BigDecimal attitudeRating;

    @Schema(description = "技术专业性评分", example = "5.0")
    @NotNull(message = "技术专业性评分不能为空")
    @DecimalMin(value = "1.0", message = "技术专业性评分不能小于1.0")
    @DecimalMax(value = "5.0", message = "技术专业性评分不能大于5.0")
    private BigDecimal professionalismRating;

    @Schema(description = "责任心评分", example = "4.0")
    @NotNull(message = "责任心评分不能为空")
    @DecimalMin(value = "1.0", message = "责任心评分不能小于1.0")
    @DecimalMax(value = "5.0", message = "责任心评分不能大于5.0")
    private BigDecimal responsibilityRating;

    @Schema(description = "评价内容", example = "阿姨服务很专业，态度也很好，工作认真负责，准时守信，非常满意！")
    @NotBlank(message = "评价内容不能为空")
    @Size(max = 500, message = "评价内容不能超过500字符")
    private String reviewContent;

    @Schema(description = "评价标签列表", example = "[\"服务周到\", \"技术专业\", \"准时守信\"]")
    @Size(max = 8, message = "评价标签不能超过8个")
    private List<String> reviewTags;

    @Schema(description = "评价图片URL列表", example = "[\"https://example.com/image1.jpg\"]")
    @Size(max = 5, message = "评价图片不能超过5张")
    private List<String> reviewImages;

    @Schema(description = "是否匿名评价", example = "false")
    private Boolean isAnonymous = false;

    @Schema(description = "是否推荐", example = "true")
    private Boolean isRecommend = false;
}

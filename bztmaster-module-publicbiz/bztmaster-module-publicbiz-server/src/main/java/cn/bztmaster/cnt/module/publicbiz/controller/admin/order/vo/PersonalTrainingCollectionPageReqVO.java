package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 个人培训订单收款列表查询 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 个人培训订单收款列表查询 VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PersonalTrainingCollectionPageReqVO extends PageParam {

    @Schema(description = "订单ID", example = "1")
    private Long orderId;

    @Schema(description = "订单号", example = "PT1234567890")
    private String orderNo;

    @Schema(description = "支付类型", example = "cash")
    private String paymentType;

    @Schema(description = "支付状态", example = "success")
    private String paymentStatus;

    @Schema(description = "操作人姓名", example = "张三")
    private String operatorName;

    @Schema(description = "开始日期", example = "2024-12-01")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-12-31")
    private LocalDate endDate;

    @Schema(description = "第三方交易号", example = "TX123456789")
    private String transactionId;
}



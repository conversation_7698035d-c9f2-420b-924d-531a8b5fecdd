package cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Pattern;
import com.mzt.logapi.starter.annotation.DiffLogField;

@Data
@Schema(description = "资源中心 - 合作伙伴编辑 Request VO")
public class PartnerUpdateReqVO {
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "ID不能为空")
    private Long id;

    @Schema(description = "机构名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "机构名称不能为空")
    @Size(max = 128, message = "机构名称长度不能超过128字符")
    @DiffLogField(name = "机构名称")
    private String name;

    @Schema(description = "机构类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "机构类型不能为空")
    @Size(max = 32, message = "机构类型长度不能超过32字符")
    @DiffLogField(name = "机构类型")
    private String type;

    @Schema(description = "机构简称")
    @DiffLogField(name = "机构简称")
    private String shortName;
    @Schema(description = "业务模块")
    @DiffLogField(name = "业务模块")
    private String biz;
    @Schema(description = "风险等级")
    @DiffLogField(name = "风险等级")
    private String risk;
    @Schema(description = "法人代表")
    @DiffLogField(name = "法人代表")
    private String legalPerson;
    @Schema(description = "成立日期")
    @DiffLogField(name = "成立日期")
    private String foundationDate;
    @Schema(description = "统一社会信用代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "统一社会信用代码不能为空")
    @Size(max = 64, message = "统一社会信用代码长度不能超过64字符")
    @DiffLogField(name = "统一社会信用代码")
    private String creditCode;
    @Schema(description = "注册地址")
    @DiffLogField(name = "注册地址")
    private String registerAddress;
    @Schema(description = "经营地址")
    @DiffLogField(name = "经营地址")
    private String businessAddress;
    @Schema(description = "主营业务")
    @DiffLogField(name = "主营业务")
    private String mainBusiness;
    @Schema(description = "主要联系人")
    @DiffLogField(name = "主要联系人")
    private String contactName;
    @Schema(description = "联系电话")
    @DiffLogField(name = "联系电话")
    private String contactPhone;

    @Schema(description = "省份code")
    @DiffLogField(name = "省份code")
    private String provinceCode;

    @Schema(description = "省份")
    @DiffLogField(name = "省份")
    private String province;

    @Schema(description = "城市code")
    @DiffLogField(name = "城市code")
    private String cityCode;

    @Schema(description = "城市")
    @DiffLogField(name = "城市")
    private String city;

    @Schema(description = "区县code")
    @DiffLogField(name = "区县code")
    private String districtCode;

    @Schema(description = "区县")
    @DiffLogField(name = "区县")
    private String district;

    @Schema(description = "详细地址")
    @DiffLogField(name = "详细地址")
    private String detailAddress;

    @Schema(description = "经度")
    @DecimalMin(value = "-180.0", message = "经度必须在-180到180之间")
    @DecimalMax(value = "180.0", message = "经度必须在-180到180之间")
    @DiffLogField(name = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    @DecimalMin(value = "-90.0", message = "纬度必须在-90到90之间")
    @DecimalMax(value = "90.0", message = "纬度必须在-90到90之间")
    @DiffLogField(name = "纬度")
    private Double latitude;

    @Schema(description = "位置精度，可选值为 high-高精度/medium-中等精度/low-低精度")
    @Pattern(regexp = "^(high|medium|low)$", message = "位置精度只能是high、medium或low")
    @DiffLogField(name = "位置精度")
    private String locationAccuracy;

    @Schema(description = "当前评级（星级）")
    @DiffLogField(name = "当前评级")
    private Integer rating;
    @Schema(description = "合作模式")
    @DiffLogField(name = "合作模式")
    private String cooperationMode;
    @Schema(description = "合同编号")
    @DiffLogField(name = "合同编号")
    private String contractNo;
    @Schema(description = "合同开始日期")
    @DiffLogField(name = "合同开始日期")
    private String contractStart;
    @Schema(description = "合同结束日期")
    @DiffLogField(name = "合同结束日期")
    private String contractEnd;
    @Schema(description = "保证金")
    @DiffLogField(name = "保证金")
    private java.math.BigDecimal deposit;
    @Schema(description = "续约提醒日期")
    @DiffLogField(name = "续约提醒日期")
    private String renewDate;
    @Schema(description = "对公账户名")
    @DiffLogField(name = "对公账户名")
    private String accountName;
    @Schema(description = "结算周期")
    @DiffLogField(name = "结算周期")
    private String settlementCycle;
    @Schema(description = "开户银行")
    @DiffLogField(name = "开户银行")
    private String bankName;
    @Schema(description = "银行账号")
    @DiffLogField(name = "银行账号")
    private String bankAccount;
    @Schema(description = "资质文件（URL或ID）")
    @DiffLogField(name = "资质文件")
    private String qualificationFile;
    @Schema(description = "开票类型")
    @DiffLogField(name = "开票类型")
    private String invoiceType;
    @Schema(description = "开票名称")
    @DiffLogField(name = "开票名称")
    private String invoiceName;
    @Schema(description = "纳税人识别号")
    @DiffLogField(name = "纳税人识别号")
    private String taxId;
    @Schema(description = "社会组织代码")
    @DiffLogField(name = "社会组织代码")
    private String orgCode;
    @Schema(description = "开票地址")
    @DiffLogField(name = "开票地址")
    private String invoiceAddress;
    @Schema(description = "开票电话")
    @DiffLogField(name = "开票电话")
    private String invoicePhone;
    @Schema(description = "开票开户银行")
    @DiffLogField(name = "开票开户银行")
    private String invoiceBank;
    @Schema(description = "开票银行账号")
    @DiffLogField(name = "开票银行账号")
    private String invoiceBankAccount;
    @Schema(description = "开票邮箱")
    @DiffLogField(name = "开票邮箱")
    private String invoiceEmail;
    @Schema(description = "开票联系人")
    @DiffLogField(name = "开票联系人")
    private String invoiceContact;
    @Schema(description = "开票资质文件（URL或ID）")
    @DiffLogField(name = "开票资质文件")
    private String invoiceQualificationFile;
    @Schema(description = "开票备注")
    @DiffLogField(name = "开票备注")
    private String invoiceRemark;

    @Schema(description = "合作状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "合作状态不能为空")
    @Size(max = 32, message = "合作状态长度不能超过32字符")
    @DiffLogField(name = "合作状态")
    private String status;

    @Schema(description = "我方负责人ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "我方负责人ID不能为空")
    @DiffLogField(name = "我方负责人", function = "getAdminUserById")
    private Long owner;

    @Schema(description = "我方负责人昵称")
    @DiffLogField(name = "我方负责人昵称")
    private String ownerName;

    @Schema(description = "关联机构ID")
    @DiffLogField(name = "关联机构ID")
    private Long agencyId;

    @Schema(description = "关联机构名称")
    @DiffLogField(name = "关联机构名称")
    private String agencyName;

    private Long tenantId;
    private String creator;
    private String updater;
    private Boolean deleted;
}

package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 个人培训与认证订单合同响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 个人培训与认证订单合同响应")
@Data
public class PersonalTrainingContractRespVO {

    @Schema(description = "合同ID", example = "1")
    private Long contractId;

    @Schema(description = "订单ID", example = "1")
    private Long orderId;

    @Schema(description = "合同类型", example = "electronic")
    private String contractType;

    @Schema(description = "合同文件URL", example = "https://example.com/contract.pdf")
    private String contractFileUrl;

    @Schema(description = "合同状态", example = "signed")
    private String contractStatus;

    @Schema(description = "签约人", example = "王小明")
    private String signer;

    @Schema(description = "签约日期", example = "2024-06-20")
    private String signDate;

    @Schema(description = "创建时间", example = "2024-06-20 10:00:00")
    private String createTime;

    @Schema(description = "更新时间", example = "2024-06-20 10:00:00")
    private String updateTime;
}


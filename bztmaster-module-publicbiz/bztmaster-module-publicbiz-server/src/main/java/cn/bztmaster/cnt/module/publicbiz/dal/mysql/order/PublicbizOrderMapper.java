package cn.bztmaster.cnt.module.publicbiz.dal.mysql.order;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import cn.hutool.core.util.StrUtil;
import java.util.ArrayList;
import java.util.Map;

/**
 * 订单主表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PublicbizOrderMapper extends BaseMapperX<PublicbizOrderDO> {

    /**
     * 通过订单号查询订单
     *
     * @param orderNo 订单号
     * @return 订单对象
     */
    default PublicbizOrderDO selectByOrderNo(String orderNo) {
        return selectOne(PublicbizOrderDO::getOrderNo, orderNo);
    }

    /**
     * 通过订单ID列表查询订单列表
     *
     * @param ids 订单ID列表
     * @return 订单列表
     */
    default List<PublicbizOrderDO> selectListByIds(Collection<Long> ids) {
        return selectList(PublicbizOrderDO::getId, ids);
    }

    /**
     * 分页查询高校实践订单
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    default PageResult<PublicbizOrderDO> selectPracticeOrderPage(UniversityPracticeOrderPageReqVO reqVO) {
        // 如果有关键词搜索，使用关联查询
        if (StrUtil.isNotBlank(reqVO.getKeyword())) {
            List<PublicbizOrderDO> list = selectPracticeOrderPageWithKeyword(reqVO);
            // 手动分页处理
            int total = list.size();
            int start = (reqVO.getPageNo() - 1) * reqVO.getPageSize();
            int end = Math.min(start + reqVO.getPageSize(), total);
            List<PublicbizOrderDO> pageList = start < total ? list.subList(start, end) : new ArrayList<>();
            return new PageResult<>(pageList, (long) total);
        }

        // 无关键词时使用简单查询
        LambdaQueryWrapperX<PublicbizOrderDO> queryWrapper = new LambdaQueryWrapperX<PublicbizOrderDO>()
                .eq(PublicbizOrderDO::getOrderType, "practice")
                .eq(PublicbizOrderDO::getBusinessLine, "高校实践")
                .eqIfPresent(PublicbizOrderDO::getOrderStatus, reqVO.getOrderStatus())
                .eqIfPresent(PublicbizOrderDO::getPaymentStatus, reqVO.getPaymentStatus())
                .eqIfPresent(PublicbizOrderDO::getManagerId, reqVO.getManagerId())
                .eqIfPresent(PublicbizOrderDO::getOpportunityId, reqVO.getOpportunityId())
                .betweenIfPresent(PublicbizOrderDO::getCreateTime, reqVO.getCreateTime());

        return selectPage(reqVO, queryWrapper.orderByDesc(PublicbizOrderDO::getId));
    }

    /**
     * 分页查询高校实践订单（支持关联查询详情表）
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    default PageResult<PublicbizOrderDO> selectPracticeOrderPageWithDetails(UniversityPracticeOrderPageReqVO reqVO) {
        // 构建基础查询条件
        LambdaQueryWrapperX<PublicbizOrderDO> queryWrapper = new LambdaQueryWrapperX<PublicbizOrderDO>()
                .eq(PublicbizOrderDO::getOrderType, "practice")
                .eq(PublicbizOrderDO::getBusinessLine, "高校实践")
                .eqIfPresent(PublicbizOrderDO::getOrderStatus, reqVO.getOrderStatus())
                .eqIfPresent(PublicbizOrderDO::getPaymentStatus, reqVO.getPaymentStatus())
                .eqIfPresent(PublicbizOrderDO::getManagerId, reqVO.getManagerId())
                .eqIfPresent(PublicbizOrderDO::getOpportunityId, reqVO.getOpportunityId())
                .betweenIfPresent(PublicbizOrderDO::getCreateTime, reqVO.getCreateTime());

        // 关键词搜索：支持项目名称、高校名称、企业名称的模糊查询
        if (StrUtil.isNotBlank(reqVO.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(PublicbizOrderDO::getProjectName, reqVO.getKeyword())
                    .or()
                    .like(PublicbizOrderDO::getManagerName, reqVO.getKeyword())
                    .or()
                    .exists("SELECT 1 FROM publicbiz_practice_order ppo WHERE ppo.order_id = publicbiz_order.id AND ppo.university_name LIKE CONCAT('%', #{keyword}, '%')")
                    .or()
                    .exists("SELECT 1 FROM publicbiz_practice_order ppo WHERE ppo.order_id = publicbiz_order.id AND ppo.enterprise_name LIKE CONCAT('%', #{keyword}, '%')"));
        }

        return selectPage(reqVO, queryWrapper.orderByDesc(PublicbizOrderDO::getId));
    }

    /**
     * 分页查询高校实践订单（优化版本，使用JOIN查询）
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    @Select("<script>" +
            "SELECT o.* FROM publicbiz_order o " +
            "<if test='reqVO.keyword != null and reqVO.keyword != \"\"'>" +
            "LEFT JOIN publicbiz_practice_order ppo ON o.id = ppo.order_id " +
            "</if>" +
            "WHERE o.order_type = 'practice' AND o.business_line = '高校实践' " +
            "<if test='reqVO.orderStatus != null and reqVO.orderStatus != \"\"'>" +
            "AND o.order_status = #{reqVO.orderStatus} " +
            "</if>" +
            "<if test='reqVO.paymentStatus != null and reqVO.paymentStatus != \"\"'>" +
            "AND o.payment_status = #{reqVO.paymentStatus} " +
            "</if>" +
            "<if test='reqVO.managerId != null'>" +
            "AND o.manager_id = #{reqVO.managerId} " +
            "</if>" +
            "<if test='reqVO.opportunityId != null and reqVO.opportunityId != \"\"'>" +
            "AND o.opportunity_id = #{reqVO.opportunityId} " +
            "</if>" +
            "<if test='reqVO.createTime != null and reqVO.createTime.length == 2'>" +
            "AND o.create_time BETWEEN #{reqVO.createTime[0]} AND #{reqVO.createTime[1]} " +
            "</if>" +
            "<if test='reqVO.keyword != null and reqVO.keyword != \"\"'>" +
            "AND (o.project_name LIKE CONCAT('%', #{reqVO.keyword}, '%') " +
            "OR o.manager_name LIKE CONCAT('%', #{reqVO.keyword}, '%') " +
            "OR ppo.university_name LIKE CONCAT('%', #{reqVO.keyword}, '%') " +
            "OR ppo.enterprise_name LIKE CONCAT('%', #{reqVO.keyword}, '%')) " +
            "</if>" +
            "AND o.deleted = 0 " +
            "ORDER BY o.id DESC" +
            "</script>")
    List<PublicbizOrderDO> selectPracticeOrderPageWithJoin(@Param("reqVO") UniversityPracticeOrderPageReqVO reqVO);

    /**
     * 分页查询高校实践订单（支持关键词搜索）
     *
     * @param reqVO 查询条件
     * @return 订单列表
     */
    List<PublicbizOrderDO> selectPracticeOrderPageWithKeyword(@Param("reqVO") UniversityPracticeOrderPageReqVO reqVO);

    /**
     * 通过负责人ID查询订单列表
     *
     * @param managerId 负责人ID
     * @return 订单列表
     */
    default List<PublicbizOrderDO> selectListByManagerId(Long managerId) {
        return selectList(PublicbizOrderDO::getManagerId, managerId);
    }

    /**
     * 通过订单状态查询订单列表
     *
     * @param orderStatus 订单状态
     * @return 订单列表
     */
    default List<PublicbizOrderDO> selectListByOrderStatus(String orderStatus) {
        return selectList(PublicbizOrderDO::getOrderStatus, orderStatus);
    }

    /**
     * 通过支付状态查询订单列表
     *
     * @param paymentStatus 支付状态
     * @return 订单列表
     */
    default List<PublicbizOrderDO> selectListByPaymentStatus(String paymentStatus) {
        return selectList(PublicbizOrderDO::getPaymentStatus, paymentStatus);
    }

    /**
     * 通过订单号前缀查询当天已生成的订单数量
     *
     * @param orderNoPrefix 订单号前缀（如：HP20250731）
     * @return 订单数量
     */
    default int selectCountByOrderNoPrefix(String orderNoPrefix) {
        Long count = selectCount(new LambdaQueryWrapperX<PublicbizOrderDO>()
                .likeRight(PublicbizOrderDO::getOrderNo, orderNoPrefix));
        return count != null ? count.intValue() : 0;
    }

    /**
     * 根据订单类型查询总金额
     *
     * @param orderType 订单类型
     * @return 总金额
     */
    @Select("SELECT COALESCE(SUM(total_amount), 0) FROM publicbiz_order WHERE order_type = #{orderType} AND deleted = 0")
    BigDecimal selectTotalAmountByOrderType(@Param("orderType") String orderType);

    /**
     * 根据订单类型和日期范围查询月度金额
     *
     * @param orderType 订单类型
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 月度金额
     */
    @Select("SELECT COALESCE(SUM(total_amount), 0) FROM publicbiz_order WHERE order_type = #{orderType} AND create_time >= #{startDate} AND create_time <= #{endDate} AND deleted = 0")
    BigDecimal selectMonthlyAmountByOrderType(@Param("orderType") String orderType,
            @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 根据状态和阿姨查询订单数量
     *
     * @param practitionerOneId 阿姨OneID
     * @param orderStatus       订单状态
     * @return 订单数量
     */
    @Select("SELECT COUNT(*) FROM publicbiz_order o " +
            "INNER JOIN publicbiz_domestic_order do ON o.id = do.order_id " +
            "WHERE do.practitioner_oneid = #{practitionerOneId} " +
            "  AND o.order_status = #{orderStatus} " +
            "  AND o.deleted = 0 " +
            "  AND do.deleted = 0")
    Integer selectCountByStatusAndPractitioner(@Param("practitionerOneId") String practitionerOneId,
            @Param("orderStatus") String orderStatus);

    /**
     * 根据创建人查询订单列表
     *
     * @param creator 创建人
     * @return 订单列表
     */
    default List<PublicbizOrderDO> selectListByCreator(String creator) {
        return selectList(PublicbizOrderDO::getCreator, creator);
    }

    /**
     * 根据创建人和订单状态查询订单列表
     *
     * @param creator     创建人
     * @param orderStatus 订单状态
     * @return 订单列表
     */
    default List<PublicbizOrderDO> selectListByCreatorAndStatus(String creator, String orderStatus) {
        return selectList(new LambdaQueryWrapperX<PublicbizOrderDO>()
                .eq(PublicbizOrderDO::getCreator, creator)
                .eq(PublicbizOrderDO::getOrderStatus, orderStatus)
                .orderByDesc(PublicbizOrderDO::getCreateTime));
    }

    /**
     * 根据创建人统计各状态订单数量
     *
     * @param creator 创建人
     * @return 各状态订单数量
     */
    @Select("SELECT order_status, COUNT(*) as count FROM publicbiz_order " +
            "WHERE creator = #{creator} AND deleted = 0 " +
            "GROUP BY order_status")
    List<Map<String, Object>> selectStatusCountsByCreator(@Param("creator") String creator);

    /**
     * 根据创建人和订单类型查询订单列表
     *
     * @param creator   创建人
     * @param orderType 订单类型
     * @return 订单列表
     */
    default List<PublicbizOrderDO> selectByCreatorAndType(String creator, String orderType) {
        return selectList(new LambdaQueryWrapperX<PublicbizOrderDO>()
                .eq(PublicbizOrderDO::getCreator, creator)
                .eq(PublicbizOrderDO::getOrderType, orderType)
                .eq(PublicbizOrderDO::getDeleted, false)
                .orderByDesc(PublicbizOrderDO::getCreateTime));
    }

}

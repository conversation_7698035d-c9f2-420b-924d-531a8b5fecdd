<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PersonalTrainingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PersonalTrainingDO">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="order_id" property="orderId" />
        <result column="order_no" property="orderNo" />
        <result column="student_oneid" property="studentOneid" />
        <result column="student_name" property="studentName" />
        <result column="student_phone" property="studentPhone" />
        <result column="student_email" property="studentEmail" />
        <result column="student_id_card" property="studentIdCard" />
        <result column="course_name" property="courseName" />
        <result column="course_type" property="courseType" />
        <result column="course_description" property="courseDescription" />
        <result column="course_duration" property="courseDuration" />
        <result column="learning_status" property="learningStatus" />
        <result column="exam_status" property="examStatus" />
        <result column="course_fee" property="courseFee" />
        <result column="exam_fee" property="examFee" />
        <result column="certification_fee" property="certificationFee" />
        <!-- 这些字段已移动到订单主表，不需要在这里映射 -->
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, creator, create_time, updater, update_time, deleted,
        order_id, order_no, student_oneid, student_name, student_phone, student_email, student_id_card,
        course_name, course_type, course_description, course_duration, learning_status, exam_status,
        course_fee, exam_fee, certification_fee
    </sql>

    <!-- 分页查询订单列表 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM publicbiz_personal_order
        <where>
            deleted = 0
            <!-- 这些字段在订单主表中，需要特殊处理 -->
            <if test="reqDTO.keyword != null and reqDTO.keyword != ''">
                AND (student_name LIKE CONCAT('%', #{reqDTO.keyword}, '%') OR course_name LIKE CONCAT('%', #{reqDTO.keyword}, '%'))
            </if>
            <if test="reqDTO.startDate != null and reqDTO.startDate != ''">
                AND DATE(create_time) >= #{reqDTO.startDate}
            </if>
            <if test="reqDTO.endDate != null and reqDTO.endDate != ''">
                AND DATE(create_time) &lt;= #{reqDTO.endDate}
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{reqDTO.page}, #{reqDTO.size}
    </select>

    <!-- 统计订单数量 -->
    <select id="selectCount" resultType="long">
        SELECT COUNT(*)
        FROM publicbiz_personal_order
        <where>
            deleted = 0
            <!-- 这些字段在订单主表中，需要特殊处理 -->
            <if test="reqDTO.keyword != null and reqDTO.keyword != ''">
                AND (student_name LIKE CONCAT('%', #{reqDTO.keyword}, '%') OR course_name LIKE CONCAT('%', #{reqDTO.keyword}, '%'))
            </if>
            <if test="reqDTO.startDate != null and reqDTO.startDate != ''">
                AND DATE(create_time) >= #{reqDTO.startDate}
            </if>
            <if test="reqDTO.endDate != null and reqDTO.endDate != ''">
                AND DATE(create_time) &lt;= #{reqDTO.endDate}
            </if>
        </where>
    </select>

    <!-- 根据订单号查询订单 -->
    <select id="selectByOrderNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM publicbiz_personal_order
        WHERE order_no = #{param1} AND deleted = 0
    </select>

    <!-- 根据ID查询订单 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM publicbiz_personal_order
        WHERE id = #{param1} AND deleted = 0
    </select>

    <!-- 插入订单 -->
    <insert id="insert" parameterType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PersonalTrainingDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO publicbiz_personal_order (
            tenant_id, creator, create_time, updater, update_time, deleted,
            order_id, order_no, student_oneid, student_name, student_phone, student_email, student_id_card,
            course_name, course_type, course_description, course_duration, learning_status, exam_status,
            course_fee, exam_fee, certification_fee
        ) VALUES (
            #{tenantId}, #{creator}, #{createTime}, #{updater}, #{updateTime}, #{deleted},
            #{orderId}, #{orderNo}, #{studentOneid}, #{studentName}, #{studentPhone}, #{studentEmail}, #{studentIdCard},
            #{courseName}, #{courseType}, #{courseDescription}, #{courseDuration}, #{learningStatus}, #{examStatus},
            #{courseFee}, #{examFee}, #{certificationFee}
        )
    </insert>

    <!-- 更新订单 -->
    <update id="updateById" parameterType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PersonalTrainingDO">
        UPDATE publicbiz_personal_order
        <set>
            <if test="updater != null">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="studentOneid != null">student_oneid = #{studentOneid},</if>
            <if test="studentName != null">student_name = #{studentName},</if>
            <if test="studentPhone != null">student_phone = #{studentPhone},</if>
            <if test="studentEmail != null">student_email = #{studentEmail},</if>
            <if test="studentIdCard != null">student_id_card = #{studentIdCard},</if>
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="courseType != null">course_type = #{courseType},</if>
            <if test="courseDescription != null">course_description = #{courseDescription},</if>
            <if test="courseDuration != null">course_duration = #{courseDuration},</if>
            <if test="learningStatus != null">learning_status = #{learningStatus},</if>
            <if test="examStatus != null">exam_status = #{examStatus},</if>
            <if test="courseFee != null">course_fee = #{courseFee},</if>
            <if test="examFee != null">exam_fee = #{examFee},</if>
            <if test="certificationFee != null">certification_fee = #{certificationFee},</if>
            <!-- 这些字段已移动到订单主表，不需要在这里更新 -->
        </set>
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 逻辑删除订单 -->
    <update id="deleteById">
        UPDATE publicbiz_personal_order
        SET deleted = 1, update_time = NOW()
        WHERE id = #{param1} AND deleted = 0
    </update>

</mapper>




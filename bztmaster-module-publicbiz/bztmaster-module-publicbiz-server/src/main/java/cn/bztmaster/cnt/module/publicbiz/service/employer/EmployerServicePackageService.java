package cn.bztmaster.cnt.module.publicbiz.service.employer;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackageRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackageQueryReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackageDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackageReviewRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.AgencyDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackageRecommendRespVO;

/**
 * 雇主端服务套餐 Service 接口
 *
 * <AUTHOR>
 */
public interface EmployerServicePackageService {

    /**
     * 分页查询服务套餐列表
     *
     * @param reqVO 查询条件
     * @return 服务套餐分页结果
     */
    PageResult<ServicePackageRespVO> getServicePackagePage(ServicePackageQueryReqVO reqVO);

    /**
     * 获取服务套餐详情
     *
     * @param id 套餐ID
     * @return 套餐详情
     */
    ServicePackageDetailRespVO getServicePackageDetail(Long id);

    /**
     * 获取服务套餐评价列表
     *
     * @param packageId 套餐ID
     * @param page      页码
     * @param size      每页大小
     * @return 评价列表
     */
    ServicePackageReviewRespVO getServicePackageReviews(Long packageId, Integer page, Integer size);

    /**
     * 获取机构详情
     *
     * @param packageId 套餐ID
     * @return 机构详情
     */
    AgencyDetailRespVO getAgencyDetail(Long packageId);

    /**
     * 获取推荐服务套餐列表
     *
     * @param packageId 当前套餐ID
     * @param page      页码
     * @param size      每页大小
     * @return 推荐套餐列表
     */
    ServicePackageRecommendRespVO getServicePackageRecommendations(Long packageId, Integer page, Integer size);

    /**
     * 清除服务套餐缓存
     */
    void clearServicePackageCache();

}

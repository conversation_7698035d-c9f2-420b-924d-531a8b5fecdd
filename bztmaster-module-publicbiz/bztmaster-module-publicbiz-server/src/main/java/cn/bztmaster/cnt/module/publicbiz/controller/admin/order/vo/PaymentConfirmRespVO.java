package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 支付确认 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 支付确认 Response VO")
@Data
public class PaymentConfirmRespVO {

    @Schema(description = "支付ID", example = "1")
    private Long paymentId;

    @Schema(description = "支付单号", example = "PAY123456789")
    private String paymentNo;
}




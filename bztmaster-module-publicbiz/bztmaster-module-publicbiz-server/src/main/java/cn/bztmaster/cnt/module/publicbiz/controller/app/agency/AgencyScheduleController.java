package cn.bztmaster.cnt.module.publicbiz.controller.app.agency;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyScheduleAuntListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyScheduleDayTaskRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyScheduleMonthRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.agency.AgencyScheduleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序 - 机构排班")
@RestController
@RequestMapping("/publicbiz/agency/{agencyId}")
@Validated
public class AgencyScheduleController {

    @Resource
    private AgencyScheduleService agencyScheduleService;

    @GetMapping("/aunts")
    @Operation(summary = "查询机构阿姨列表（轻量）")
    @PermitAll
    public CommonResult<List<AgencyScheduleAuntListRespVO>> getAgencyAunts(
            @Parameter(description = "机构ID", required = true) @PathVariable("agencyId") @NotNull Long agencyId,
            @Parameter(description = "状态：active|inactive|pending") @RequestParam(value = "status", required = false) String status,
            @Parameter(description = "关键字：姓名/手机号") @RequestParam(value = "keyword", required = false) String keyword) {
        List<AgencyScheduleAuntListRespVO> list = agencyScheduleService.getAgencyAunts(agencyId, status, keyword);
        return success(list);
    }

    @GetMapping("/schedules/month")
    @Operation(summary = "查询当月排班汇总（按阿姨+天计数）")
    @PermitAll
    public CommonResult<AgencyScheduleMonthRespVO> getMonthSchedules(
            @Parameter(description = "机构ID", required = true) @PathVariable("agencyId") @NotNull Long agencyId,
            @Parameter(description = "年份", required = true) @RequestParam("year") Integer year,
            @Parameter(description = "月份(1-12)", required = true) @RequestParam("month") Integer month) {
        AgencyScheduleMonthRespVO resp = agencyScheduleService.getMonthSchedules(agencyId, year, month);
        return success(resp);
    }

    @GetMapping("/aunts/{auntId}/schedules/day")
    @Operation(summary = "查询阿姨某天任务明细")
    @PermitAll
    public CommonResult<List<AgencyScheduleDayTaskRespVO>> getDayTasks(
            @Parameter(description = "机构ID", required = true) @PathVariable("agencyId") @NotNull Long agencyId,
            @Parameter(description = "阿姨ID", required = true) @PathVariable("auntId") @NotNull Long auntId,
            @Parameter(description = "日期 yyyy-MM-dd", required = true) @RequestParam("date") String dateStr) {
        LocalDate date = LocalDate.parse(dateStr);
        List<AgencyScheduleDayTaskRespVO> list = agencyScheduleService.getDayTasks(agencyId, auntId, date);
        return success(list);
    }
}



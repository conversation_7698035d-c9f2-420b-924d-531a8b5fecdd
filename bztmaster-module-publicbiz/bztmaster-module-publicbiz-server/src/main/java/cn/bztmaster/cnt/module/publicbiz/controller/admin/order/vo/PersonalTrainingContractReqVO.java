package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 个人培训与认证订单合同请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 个人培训与认证订单合同请求")
@Data
public class PersonalTrainingContractReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "合同类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "electronic")
    @NotNull(message = "合同类型不能为空")
    private String contractType;

    @Schema(description = "合同文件URL", example = "https://example.com/contract.pdf")
    private String contractFileUrl;

    @Schema(description = "合同状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "signed")
    @NotNull(message = "合同状态不能为空")
    private String contractStatus;

    @Schema(description = "签约人", example = "王小明")
    private String signer;

    @Schema(description = "签约日期", example = "2024-06-20")
    private String signDate;

    @Schema(description = "备注信息", example = "合同确认")
    private String remark;
}


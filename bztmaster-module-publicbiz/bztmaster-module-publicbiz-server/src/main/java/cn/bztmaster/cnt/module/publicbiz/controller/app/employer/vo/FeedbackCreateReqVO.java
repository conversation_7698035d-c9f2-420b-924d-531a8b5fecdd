package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

/**
 * 新增意见反馈 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "新增意见反馈 Request VO")
@Data
public class FeedbackCreateReqVO {

    @Schema(description = "订单ID", example = "TX20241219001")
    @NotBlank(message = "订单ID不能为空")
    private String orderId;

    @Schema(description = "投诉类型列表", example = "[\"service_attitude\", \"professional_ability\"]")
    @NotEmpty(message = "投诉类型不能为空")
    @Size(max = 5, message = "投诉类型不能超过5个")
    private List<String> complaintTypes;

    @Schema(description = "投诉内容", example = "阿姨在清洁过程中态度敷衍,清洁效果不理想,要求换阿姨")
    @NotBlank(message = "投诉内容不能为空")
    @Size(max = 500, message = "投诉内容不能超过500字符")
    private String complaintContent;

    @Schema(description = "投诉图片URL列表", example = "[\"https://example.com/image1.jpg\"]")
    @Size(max = 5, message = "投诉图片不能超过5张")
    private List<String> complaintImages;
}

package cn.bztmaster.cnt.module.publicbiz.service.employer.impl;

import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderCreateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderCreateRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.PackagesAndAuntInfoRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.OrderReviewReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.OrderReviewRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ReplaceRequestReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ReplaceRequestRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.InProgressOrderRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.AuntReviewDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderLogDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderLogDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserAddressDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.DomesticOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic.DomesticTaskMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.leads.LeadInfoMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderLogMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.workorder.WorkOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.workorder.WorkOrderLogMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.MpUserMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.MpUserAddressMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.ServicePackageMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic.DomesticTaskMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.AuntReviewMapper;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.BusinessModuleEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.CreateMethodEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.LeadSourceEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.LeadStatusEnum;
import cn.bztmaster.cnt.module.publicbiz.service.employer.EmployerOrderService;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.infra.api.file.FileApi;
import cn.bztmaster.cnt.module.publicbiz.service.file.FileUploadService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.UUID;
import java.util.stream.Collectors;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 雇主端订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class EmployerOrderServiceImpl implements EmployerOrderService {

    @Resource
    private PublicbizOrderMapper orderMapper;

    @Resource
    private DomesticOrderMapper domesticOrderMapper;

    @Resource
    private LeadInfoMapper leadInfoMapper;

    @Resource
    private PublicbizOrderLogMapper orderLogMapper;

    @Resource
    private MpUserMapper mpUserMapper;

    @Resource
    private MpUserAddressMapper mpUserAddressMapper;

    @Resource
    private ServicePackageMapper servicePackageMapper;

    @Resource
    private PractitionerMapper practitionerMapper;

    @Resource
    private DomesticTaskMapper domesticTaskMapper;

    @Resource
    private AuntReviewMapper auntReviewMapper;

    @Resource
    private FileApi fileApi;

    @Resource
    private FileUploadService fileUploadService;

    @Resource
    private WorkOrderMapper workOrderMapper;

    @Resource
    private WorkOrderLogMapper workOrderLogMapper;

    @Override
    public EmployerOrderListRespVO getOrderList(String customerOpenId, String status, Integer page, Integer size) {
        log.info("获取雇主订单列表 - customerOpenId: {}, status: {}, page: {}, size: {}",
                customerOpenId, status, page, size);

        try {
            // 1. 参数校验
            if (StrUtil.isEmpty(customerOpenId)) {
                throw new ServiceException(400, "客户OpenId不能为空");
            }

            // 2. 根据openId获取用户oneid
            String userOneId = getOneIdByOpenId(customerOpenId);
            if (StrUtil.isEmpty(userOneId)) {
                throw new ServiceException(404, "客户信息不存在");
            }

            // 3. 设置默认分页参数
            page = page == null || page <= 0 ? 1 : page;
            size = size == null || size <= 0 ? 10 : Math.min(size, 50);

            // 4. 查询订单列表
            List<PublicbizOrderDO> orderList;
            if ("all".equals(status) || StrUtil.isEmpty(status)) {
                // 查询所有订单
                orderList = orderMapper.selectListByCreator(userOneId);
            } else {
                // 根据状态查询订单
                orderList = orderMapper.selectListByCreatorAndStatus(userOneId, status);
            }

            // 5. 过滤家政服务订单
            List<PublicbizOrderDO> domesticOrders = orderList.stream()
                    .filter(order -> "domestic".equals(order.getOrderType()))
                    .collect(Collectors.toList());

            // 6. 分页处理
            int total = domesticOrders.size();
            int startIndex = (page - 1) * size;
            int endIndex = Math.min(startIndex + size, total);
            List<PublicbizOrderDO> pageOrders = startIndex < total ? domesticOrders.subList(startIndex, endIndex)
                    : new ArrayList<>();

            // 7. 获取订单详情
            List<EmployerOrderListRespVO.OrderInfo> orderInfoList = new ArrayList<>();
            for (PublicbizOrderDO order : pageOrders) {
                DomesticOrderDO domesticOrder = domesticOrderMapper.selectByOrderId(order.getId());
                if (domesticOrder != null) {
                    EmployerOrderListRespVO.OrderInfo orderInfo = buildOrderInfo(order, domesticOrder);
                    orderInfoList.add(orderInfo);
                }
            }

            // 8. 统计各状态订单数量
            Map<String, Long> statusCounts = getStatusCounts(userOneId);

            // 9. 构建响应结果
            EmployerOrderListRespVO result = new EmployerOrderListRespVO();
            result.setTotal((long) total);
            result.setPages((long) ((total + size - 1) / size));
            result.setCurrent((long) page);
            result.setSize((long) size);
            result.setRecords(orderInfoList);
            result.setStatusCounts(statusCounts);

            log.info("获取雇主订单列表成功 - 总数: {}, 当前页: {}, 每页数量: {}", total, page, size);
            return result;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取雇主订单列表失败 - customerOpenId: {}, 错误: {}", customerOpenId, e.getMessage(), e);
            throw new ServiceException(500, "获取订单列表失败，请稍后重试");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EmployerOrderCreateRespVO createOrder(@Valid EmployerOrderCreateReqVO createReqVO) {
        log.info("创建服务套餐订单 - createReqVO: {}", createReqVO);

        try {
            // 1. 参数校验（@Valid注解已处理）

            // 根据openId获取用户oneid作为创建人和更新人
            String userOneId = getOneIdByOpenId(createReqVO.getOpenId());

            // 2. 获取并校验服务套餐信息
            ServicePackageDO servicePackage = validateAndGetServicePackage(createReqVO);

            // 3. 业务规则校验
            validateBusinessRules(createReqVO);

            // 4. 线索匹配或创建
            String leadId = handleLeadMatching(createReqVO);

            // 5. 生成订单号
            String orderNo = generateOrderNo();

            // 6. 创建订单主表记录
            PublicbizOrderDO orderDO = createMainOrder(createReqVO, orderNo, leadId, userOneId, servicePackage);

            // 7. 创建家政服务订单详情记录
            DomesticOrderDO domesticOrderDO = createDomesticOrder(createReqVO, orderDO, userOneId, servicePackage);

            // 8. 创建订单日志
            createOrderLog(orderDO, "订单创建", "雇主通过小程序创建订单", "draft", "pending_payment", userOneId);

            // 9. 根据服务时间拆分子任务单
            createSubTasks(orderDO, domesticOrderDO, createReqVO, servicePackage, userOneId);

            // 10. 构建响应结果
            EmployerOrderCreateRespVO result = buildCreateResponse(orderDO, domesticOrderDO);

            log.info("订单创建成功 - 订单号: {}, 订单ID: {}", orderNo, orderDO.getId());
            return result;

        } catch (Exception e) {
            log.error("创建订单失败 - createReqVO: {}, 错误: {}", createReqVO, e.getMessage(), e);
            throw new ServiceException(500, "创建订单失败，请稍后重试");
        }
    }

    /**
     * 获取并校验服务套餐信息
     */
    private ServicePackageDO validateAndGetServicePackage(EmployerOrderCreateReqVO createReqVO) {
        Long serviceId = createReqVO.getServiceId();
        if (serviceId == null) {
            throw new ServiceException(400, "服务套餐ID不能为空");
        }

        // 查询数据库中的最新套餐信息
        ServicePackageDO servicePackage = servicePackageMapper.selectById(serviceId);
        if (servicePackage == null) {
            throw new ServiceException(400, "服务套餐不存在");
        }

        // 检查套餐状态
        if (!"active".equals(servicePackage.getStatus())) {
            throw new ServiceException(400, "服务套餐已下架，请选择其他套餐");
        }

        // 检查审核状态
        if (!"approved".equals(servicePackage.getAuditStatus())) {
            throw new ServiceException(400, "服务套餐审核未通过，请选择其他套餐");
        }

        // 校验价格是否一致
        BigDecimal requestPrice = createReqVO.getServiceInfo().getPrice();
        BigDecimal dbPrice = servicePackage.getPrice();

        if (requestPrice.compareTo(dbPrice) != 0) {
            log.warn("套餐价格不一致 - 请求价格: {}, 数据库价格: {}, 套餐ID: {}",
                    requestPrice, dbPrice, serviceId);
            throw new ServiceException(400, "套餐信息已变更，请刷新套餐信息后提交订单");
        }

        // 校验原价是否一致
        BigDecimal requestOriginalPrice = createReqVO.getServiceInfo().getOriginalPrice();
        BigDecimal dbOriginalPrice = servicePackage.getOriginalPrice();

        if (requestOriginalPrice != null && dbOriginalPrice != null &&
                requestOriginalPrice.compareTo(dbOriginalPrice) != 0) {
            log.warn("套餐原价不一致 - 请求原价: {}, 数据库原价: {}, 套餐ID: {}",
                    requestOriginalPrice, dbOriginalPrice, serviceId);
            throw new ServiceException(400, "套餐信息已变更，请刷新套餐信息后提交订单");
        }

        log.info("套餐信息校验通过 - 套餐ID: {}, 套餐名称: {}, 价格: {}",
                serviceId, servicePackage.getName(), dbPrice);

        return servicePackage;
    }

    /**
     * 业务规则校验
     */
    private void validateBusinessRules(EmployerOrderCreateReqVO createReqVO) {
        // 校验套餐类型和参数的匹配性
        if ("long-term".equals(createReqVO.getPackageType())) {
            if (StrUtil.isEmpty(createReqVO.getStartDate()) || createReqVO.getServiceDays() == null) {
                throw new ServiceException(400, "长周期套餐必须提供开始日期和服务天数");
            }
        } else if ("count-card".equals(createReqVO.getPackageType())) {
            // 校验次卡套餐的服务时间
            if (createReqVO.getServiceTimes() == null || createReqVO.getServiceTimes().isEmpty()) {
                throw new ServiceException(400, "请至少选择一个服务时间");
            }

            // 校验服务时间格式
            for (String serviceTime : createReqVO.getServiceTimes()) {
                if (StrUtil.isEmpty(serviceTime)) {
                    throw new ServiceException(400, "服务时间不能为空");
                }
                try {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
                    LocalDateTime.parse(serviceTime, formatter);
                } catch (Exception e) {
                    throw new ServiceException(400, "服务时间格式不正确，请使用 yyyy-MM-dd HH:mm 格式");
                }
            }
        }

        // 校验金额
        if (createReqVO.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException(400, "订单金额必须大于0");
        }
    }

    /**
     * 处理线索匹配
     */
    private String handleLeadMatching(EmployerOrderCreateReqVO createReqVO) {
        String customerPhone = createReqVO.getUserPhone();

        // 查询是否存在该手机号的线索
        List<LeadInfoDO> existingLeads = leadInfoMapper.selectListByCustomerPhone(customerPhone);

        if (!existingLeads.isEmpty()) {
            // 存在历史线索，更新状态为已转化，并更新客户姓名为真实昵称
            LeadInfoDO existingLead = existingLeads.get(0); // 取最新的线索
            String customerName = getCustomerNameByOpenId(createReqVO.getOpenId(), createReqVO.getUserPhone());
            existingLead.setCustomerName(customerName);
            existingLead.setLeadStatus(LeadStatusEnum.CONVERTED.getType());
            existingLead.setUpdateTime(LocalDateTime.now());
            leadInfoMapper.updateById(existingLead);

            log.info("找到历史线索，更新状态为已转化 - 线索ID: {}, 客户手机号: {}, 客户姓名: {}",
                    existingLead.getLeadId(), customerPhone, customerName);

            return existingLead.getLeadId();
        } else {
            // 不存在线索，创建新线索并标记为已转化
            String leadId = generateLeadId();
            LeadInfoDO newLead = createNewLead(createReqVO, leadId);
            leadInfoMapper.insert(newLead);

            log.info("创建新线索并标记为已转化 - 线索ID: {}, 客户手机号: {}", leadId, customerPhone);

            return leadId;
        }
    }

    /**
     * 创建新线索
     */
    private LeadInfoDO createNewLead(EmployerOrderCreateReqVO createReqVO, String leadId) {
        // 根据openId查询用户昵称
        String customerName = getCustomerNameByOpenId(createReqVO.getOpenId(), createReqVO.getUserPhone());

        LeadInfoDO leadDO = new LeadInfoDO();
        leadDO.setLeadId(leadId);
        leadDO.setCustomerName(customerName);
        leadDO.setCustomerPhone(createReqVO.getUserPhone());
        leadDO.setLeadSource(LeadSourceEnum.OTHER.getType()); // 其它
        leadDO.setBusinessModule(BusinessModuleEnum.DOMESTIC_SERVICE.getType()); // 家政业务
        leadDO.setLeadStatus(LeadStatusEnum.CONVERTED.getType()); // 已转化
        leadDO.setCreateMethod(CreateMethodEnum.API_INTEGRATION.getType());
        leadDO.setRemark("雇主通过小程序创建订单自动生成线索");
        leadDO.setCreateTime(LocalDateTime.now());
        leadDO.setUpdateTime(LocalDateTime.now());
        leadDO.setTenantId(1L);
        return leadDO;
    }

    /**
     * 根据openId获取用户昵称
     */
    private String getCustomerNameByOpenId(String openId, String userPhone) {
        if (StrUtil.isEmpty(openId)) {
            log.warn("openId为空，使用雇主手机号作为客户姓名");
            return userPhone;
        }

        try {
            MpUserDO mpUser = mpUserMapper.selectByOpenid(openId);
            if (mpUser != null && StrUtil.isNotEmpty(mpUser.getNickname())) {
                log.info("根据openId查询到用户昵称 - openId: {}, nickname: {}", openId, mpUser.getNickname());
                return mpUser.getNickname();
            } else {
                log.warn("根据openId未查询到用户信息或昵称为空 - openId: {}", openId);
                return userPhone;
            }
        } catch (Exception e) {
            log.error("根据openId查询用户信息失败 - openId: {}, 错误: {}", openId, e.getMessage(), e);
            return userPhone;
        }
    }

    /**
     * 根据openId获取用户oneid
     */
    private String getOneIdByOpenId(String openId) {
        if (StrUtil.isEmpty(openId)) {
            log.warn("openId为空，无法获取用户oneid");
            return null;
        }

        try {
            MpUserDO mpUser = mpUserMapper.selectByOpenid(openId);
            if (mpUser != null && StrUtil.isNotEmpty(mpUser.getOneid())) {
                log.info("根据openId查询到用户oneid - openId: {}, oneid: {}", openId, mpUser.getOneid());
                return mpUser.getOneid();
            } else {
                log.warn("根据openId未查询到用户信息或oneid为空 - openId: {}", openId);
                return null;
            }
        } catch (Exception e) {
            log.error("根据openId查询用户信息失败 - openId: {}, 错误: {}", openId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据addressId获取地址信息，处理省市区重复去重
     */
    private String getAddressByAddressId(Long addressId) {
        if (addressId == null) {
            log.warn("addressId为空，返回空地址");
            return "";
        }

        try {
            MpUserAddressDO addressDO = mpUserAddressMapper.selectById(addressId);
            if (addressDO == null) {
                log.warn("根据addressId未查询到地址信息 - addressId: {}", addressId);
                return "";
            }

            // 获取region和address
            String region = addressDO.getRegion();
            String address = addressDO.getAddress();

            if (StrUtil.isEmpty(region) && StrUtil.isEmpty(address)) {
                log.warn("地址信息为空 - addressId: {}", addressId);
                return "";
            }

            // 处理省市区重复去重
            String fullAddress = buildFullAddress(region, address);
            log.info("根据addressId查询到地址信息 - addressId: {}, fullAddress: {}", addressId, fullAddress);
            return fullAddress;

        } catch (Exception e) {
            log.error("根据addressId查询地址信息失败 - addressId: {}, 错误: {}", addressId, e.getMessage(), e);
            return "";
        }
    }

    /**
     * 构建完整地址，处理省市区重复去重
     */
    private String buildFullAddress(String region, String address) {
        if (StrUtil.isEmpty(region) && StrUtil.isEmpty(address)) {
            return "";
        }

        if (StrUtil.isEmpty(region)) {
            return address;
        }

        if (StrUtil.isEmpty(address)) {
            return region;
        }

        // 检查address是否已经包含了region中的省市区信息
        // 先检查完整的region是否包含在address中
        if (address.contains(region)) {
            log.info("详细地址已包含完整省市区信息，去重处理 - region: {}, address: {}", region, address);
            return address;
        }

        // 再检查region中的各个部分是否包含在address中
        String[] regionParts = region.split("(?<=省|市|区|县)");
        boolean hasDuplicate = false;
        for (String part : regionParts) {
            if (StrUtil.isNotEmpty(part) && address.contains(part)) {
                hasDuplicate = true;
                break;
            }
        }

        if (hasDuplicate) {
            // 如果详细地址中已经包含了省市区信息，则只返回详细地址
            log.info("详细地址已包含省市区信息，去重处理 - region: {}, address: {}", region, address);
            return address;
        }

        // 如果没有重复，则拼接region和address
        return region + address;
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        String dateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        long snowflakeId = IdUtil.getSnowflake().nextId();
        String suffix = String.format("%03d", snowflakeId % 1000);
        return "HM" + dateTimeStr + suffix;
    }

    /**
     * 生成线索ID
     */
    private String generateLeadId() {
        String dateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        long snowflakeId = IdUtil.getSnowflake().nextId();
        String suffix = String.format("%03d", snowflakeId % 1000);
        return "XS" + dateTimeStr + suffix;
    }

    /**
     * 创建订单主表记录
     */
    private PublicbizOrderDO createMainOrder(EmployerOrderCreateReqVO createReqVO, String orderNo, String leadId,
            String userOneId, ServicePackageDO servicePackage) {

        PublicbizOrderDO orderDO = PublicbizOrderDO.builder()
                .orderNo(orderNo)
                .orderType("domestic") // 家政服务
                .businessLine("家政服务")
                .leadId(leadId)
                .projectName(servicePackage.getName()) // 使用数据库中的套餐名称
                .projectDescription(servicePackage.getServiceDescription()) // 使用数据库中的套餐描述
                .startDate(StrUtil.isNotEmpty(createReqVO.getStartDate()) ? LocalDate.parse(createReqVO.getStartDate())
                        : null)
                .totalAmount(createReqVO.getTotalAmount())
                .paidAmount(BigDecimal.ZERO)
                .refundAmount(BigDecimal.ZERO)
                .paymentStatus("pending")
                .orderStatus("pending_payment")
                .remark(createReqVO.getRemark())
                .settlementStatus("pending")
                .isSelectedForReconciliation(0)
                .build();

        // 设置创建人和更新人为用户的oneid
        orderDO.setCreator(userOneId);
        orderDO.setUpdater(userOneId);
        orderDO.setTenantId(1L);

        orderMapper.insert(orderDO);
        return orderDO;
    }

    /**
     * 创建家政服务订单详情记录
     */
    private DomesticOrderDO createDomesticOrder(EmployerOrderCreateReqVO createReqVO, PublicbizOrderDO orderDO,
            String userOneId, ServicePackageDO servicePackage) {
        String orderItemNo = generateOrderNo();

        // 根据addressId获取地址信息
        String customerAddress = getAddressByAddressId(createReqVO.getAddressId());
        String serviceAddress = getAddressByAddressId(createReqVO.getAddressId());

        DomesticOrderDO domesticOrderDO = new DomesticOrderDO();
        domesticOrderDO.setOrderId(orderDO.getId());
        domesticOrderDO.setOrderNo(orderItemNo);
        domesticOrderDO.setCustomerOneid(userOneId); // 使用雇主的openId关联mpUser表获取的oneid
        domesticOrderDO.setCustomerName(servicePackage.getAgencyName()); // 使用数据库中的机构名称
        domesticOrderDO.setCustomerPhone(createReqVO.getUserPhone());
        domesticOrderDO.setCustomerAddress(customerAddress); // 根据addressId查询mp_user_address表获取地址
        domesticOrderDO.setCustomerRemark(createReqVO.getRemark());
        domesticOrderDO.setServiceCategoryId(servicePackage.getCategoryId()); // 使用数据库中的分类ID
        domesticOrderDO.setServiceCategoryName(servicePackage.getCategory()); // 使用数据库中的分类名称
        domesticOrderDO.setServicePackageId(servicePackage.getId()); // 使用数据库中的套餐ID
        domesticOrderDO.setServicePackageName(servicePackage.getName()); // 使用数据库中的套餐名称
        domesticOrderDO.setServiceStartDate(
                StrUtil.isNotEmpty(createReqVO.getStartDate()) ? DateUtil.parseDate(createReqVO.getStartDate()) : null);
        domesticOrderDO.setServicePackageThumbnail(servicePackage.getThumbnail()); // 使用数据库中的套餐主图
        domesticOrderDO.setServicePackagePrice(servicePackage.getPrice()); // 使用数据库中的套餐价格
        domesticOrderDO.setServicePackageOriginalPrice(servicePackage.getOriginalPrice()); // 使用数据库中的套餐原价
        domesticOrderDO.setServicePackageUnit(servicePackage.getUnit()); // 使用数据库中的价格单位
        domesticOrderDO.setServicePackageType(servicePackage.getPackageType()); // 使用数据库中的套餐类型
        domesticOrderDO.setServiceDescription(servicePackage.getServiceDescription()); // 使用数据库中的服务描述
        domesticOrderDO.setServiceTimes(createReqVO.getServiceCount() != null ? createReqVO.getServiceCount() : 1);
        domesticOrderDO.setUnitPrice(servicePackage.getPrice()); // 使用数据库中的套餐价格作为单价
        domesticOrderDO.setTotalAmount(createReqVO.getTotalAmount());
        domesticOrderDO.setDiscountAmount(BigDecimal.ZERO);
        domesticOrderDO.setActualAmount(createReqVO.getTotalAmount());
        domesticOrderDO.setServiceAddress(serviceAddress); // 根据addressId查询mp_user_address表获取地址
        domesticOrderDO.setAgencyId(servicePackage.getAgencyId()); // 使用数据库中的机构ID
        domesticOrderDO.setAgencyName(servicePackage.getAgencyName()); // 使用数据库中的机构名称
        domesticOrderDO.setTaskCount(0);
        domesticOrderDO.setCompletedTaskCount(0);
        domesticOrderDO.setTaskProgress(BigDecimal.ZERO);
        domesticOrderDO.setCreateTime(new Date());
        domesticOrderDO.setUpdateTime(new Date());
        domesticOrderDO.setCreator(userOneId); // 设置创建人为雇主的oneid
        domesticOrderDO.setUpdater(userOneId); // 设置更新人为雇主的oneid
        domesticOrderDO.setTenantId(1L);
        domesticOrderDO.setServiceDuration(servicePackage.getSingleDurationHours() + "小时");
        domesticOrderDO.setServiceFrequency(buildServiceFrequency(servicePackage.getServiceIntervalType(),
                servicePackage.getServiceIntervalValue()));

        String serviceSchedule = buildServiceSchedule(createReqVO, servicePackage);
        log.info("设置服务时间安排 - serviceSchedule: {}", serviceSchedule);
        domesticOrderDO.setServiceSchedule(serviceSchedule);

        domesticOrderMapper.insert(domesticOrderDO);
        return domesticOrderDO;
    }

    /**
     * 创建订单日志
     */
    private void createOrderLog(PublicbizOrderDO orderDO, String logTitle, String logContent,
            String oldStatus, String newStatus, String userOneId) {
        // 构建JSON格式的日志内容
        String jsonLogContent = buildJsonLogContent(logTitle, logContent, orderDO.getProjectName());

        PublicbizOrderLogDO logDO = PublicbizOrderLogDO.builder()
                .orderNo(orderDO.getOrderNo())
                .logType("订单创建")
                .logTitle(logTitle)
                .logContent(jsonLogContent)
                .oldStatus(oldStatus)
                .newStatus(newStatus)
                .operatorId(null) // 系统操作
                .operatorName(userOneId)
                .operatorRole("雇主")
                .relatedPartyType("雇主")
                .relatedPartyName(orderDO.getProjectName())
                .build();

        orderLogMapper.insert(logDO);
    }

    /**
     * 构建JSON格式的日志内容
     */
    private String buildJsonLogContent(String action, String description, String projectName) {
        try {
            // 构建日志详情Map
            Map<String, Object> logDetails = new HashMap<>();
            logDetails.put("action", action);
            logDetails.put("details", new HashMap<>());
            logDetails.put("description", description + "，项目：" + projectName);

            return JSONUtil.toJsonStr(logDetails);
        } catch (Exception e) {
            log.error("构建JSON日志内容失败", e);
            // 如果JSON构建失败，返回简单的文本格式
            return "{\"action\":\"" + action + "\",\"details\":{},\"description\":\"" + description + "，项目："
                    + projectName + "\"}";
        }
    }

    /**
     * 构建创建响应
     */
    private EmployerOrderCreateRespVO buildCreateResponse(PublicbizOrderDO orderDO, DomesticOrderDO domesticOrderDO) {
        EmployerOrderCreateRespVO result = new EmployerOrderCreateRespVO();
        result.setOrderId(String.valueOf(orderDO.getId()));
        result.setOrderNo(orderDO.getOrderNo());
        result.setStatus(orderDO.getOrderStatus());
        result.setCreateTime(orderDO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        result.setPayUrl(""); // 支付链接，后续集成支付系统时设置

        return result;
    }

    /**
     * 构建订单信息
     */
    private EmployerOrderListRespVO.OrderInfo buildOrderInfo(PublicbizOrderDO order, DomesticOrderDO domesticOrder) {
        EmployerOrderListRespVO.OrderInfo orderInfo = new EmployerOrderListRespVO.OrderInfo();
        orderInfo.setId(order.getId());
        orderInfo.setOrderNo(order.getOrderNo());
        orderInfo.setOrderType(order.getOrderType());
        orderInfo.setBusinessLine(order.getBusinessLine());
        orderInfo.setTotalAmount(order.getTotalAmount() != null ? order.getTotalAmount().toString() : "0.00");
        orderInfo.setPaidAmount(order.getPaidAmount() != null ? order.getPaidAmount().toString() : "0.00");
        orderInfo.setRefundAmount(order.getRefundAmount() != null ? order.getRefundAmount().toString() : "0.00");
        orderInfo.setPaymentStatus(order.getPaymentStatus());
        orderInfo.setOrderStatus(order.getOrderStatus());
        orderInfo.setCreateTime(order.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderInfo.setUpdateTime(order.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 查询订单是否已评价
        Boolean isReviewed = checkOrderReviewed(order.getId());
        orderInfo.setIsReviewed(isReviewed);

        // 检查是否存在审批中的换人申请
        Boolean hasProgress = checkSubstitutionRequestInProgress(order.getOrderNo());
        orderInfo.setHasProgress(hasProgress);

        // 构建家政订单详情
        EmployerOrderListRespVO.DomesticOrderInfo domesticOrderInfo = buildDomesticOrderInfo(domesticOrder);
        orderInfo.setDomesticOrder(domesticOrderInfo);

        return orderInfo;
    }

    /**
     * 构建家政订单详情信息
     */
    private EmployerOrderListRespVO.DomesticOrderInfo buildDomesticOrderInfo(DomesticOrderDO domesticOrder) {
        EmployerOrderListRespVO.DomesticOrderInfo domesticOrderInfo = new EmployerOrderListRespVO.DomesticOrderInfo();
        domesticOrderInfo.setId(domesticOrder.getId());
        domesticOrderInfo.setCustomerName(domesticOrder.getCustomerName());
        domesticOrderInfo.setCustomerPhone(maskPhoneNumber(domesticOrder.getCustomerPhone()));
        domesticOrderInfo.setCustomerAddress(domesticOrder.getCustomerAddress());
        domesticOrderInfo.setServiceCategoryName(domesticOrder.getServiceCategoryName());
        domesticOrderInfo.setServicePackageName(domesticOrder.getServicePackageName());
        domesticOrderInfo.setServicePackageThumbnail(domesticOrder.getServicePackageThumbnail());
        domesticOrderInfo.setServicePackagePrice(
                domesticOrder.getServicePackagePrice() != null ? domesticOrder.getServicePackagePrice().toString()
                        : "0.00");
        domesticOrderInfo.setServicePackageOriginalPrice(domesticOrder.getServicePackageOriginalPrice() != null
                ? domesticOrder.getServicePackageOriginalPrice().toString()
                : "0.00");
        domesticOrderInfo.setServicePackageUnit(domesticOrder.getServicePackageUnit());
        domesticOrderInfo.setServicePackageDuration(domesticOrder.getServicePackageDuration());
        domesticOrderInfo.setServicePackageType(domesticOrder.getServicePackageType());
        domesticOrderInfo.setServiceTimes(domesticOrder.getServiceTimes());
        domesticOrderInfo
                .setUnitPrice(domesticOrder.getUnitPrice() != null ? domesticOrder.getUnitPrice().toString() : "0.00");
        domesticOrderInfo.setActualAmount(
                domesticOrder.getActualAmount() != null ? domesticOrder.getActualAmount().toString() : "0.00");
        domesticOrderInfo.setServiceAddress(domesticOrder.getServiceAddress());
        domesticOrderInfo.setServiceAddressDetail(domesticOrder.getServiceAddressDetail());
        domesticOrderInfo.setPractitionerName(domesticOrder.getPractitionerName());
        domesticOrderInfo.setPractitionerPhone(maskPhoneNumber(domesticOrder.getPractitionerPhone()));
        domesticOrderInfo.setAgencyName(domesticOrder.getAgencyName());
        domesticOrderInfo.setTaskCount(domesticOrder.getTaskCount());
        domesticOrderInfo.setCompletedTaskCount(domesticOrder.getCompletedTaskCount());
        domesticOrderInfo.setTaskProgress(
                domesticOrder.getTaskProgress() != null ? domesticOrder.getTaskProgress().toString() : "0.00");

        // 设置服务开始和结束日期
        if (domesticOrder.getServiceStartDate() != null) {
            domesticOrderInfo.setServiceStartDate(DateUtil.formatDate(domesticOrder.getServiceStartDate()));
        }
        if (domesticOrder.getServiceEndDate() != null) {
            domesticOrderInfo.setServiceEndDate(DateUtil.formatDate(domesticOrder.getServiceEndDate()));
        }

        return domesticOrderInfo;
    }

    /**
     * 手机号脱敏处理
     */
    private String maskPhoneNumber(String phone) {
        if (StrUtil.isEmpty(phone) || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }

    /**
     * 获取各状态订单数量统计
     */
    private Map<String, Long> getStatusCounts(String userOneId) {
        Map<String, Long> statusCounts = new HashMap<>();

        try {
            // 查询所有家政服务订单
            List<PublicbizOrderDO> allOrders = orderMapper.selectListByCreator(userOneId);
            List<PublicbizOrderDO> domesticOrders = allOrders.stream()
                    .filter(order -> "domestic".equals(order.getOrderType()))
                    .collect(Collectors.toList());

            // 统计各状态数量
            long allCount = domesticOrders.size();
            long pendingPaymentCount = domesticOrders.stream()
                    .filter(order -> "pending_payment".equals(order.getOrderStatus()))
                    .count();
            long executingCount = domesticOrders.stream()
                    .filter(order -> "executing".equals(order.getOrderStatus()))
                    .count();
            long completedCount = domesticOrders.stream()
                    .filter(order -> "completed".equals(order.getOrderStatus()))
                    .count();
            long cancelledCount = domesticOrders.stream()
                    .filter(order -> "cancelled".equals(order.getOrderStatus()))
                    .count();

            statusCounts.put("all", allCount);
            statusCounts.put("pending_payment", pendingPaymentCount);
            statusCounts.put("executing", executingCount);
            statusCounts.put("completed", completedCount);
            statusCounts.put("cancelled", cancelledCount);

        } catch (Exception e) {
            log.error("统计订单状态数量失败 - userOneId: {}, 错误: {}", userOneId, e.getMessage(), e);
            // 设置默认值
            statusCounts.put("all", 0L);
            statusCounts.put("pending_payment", 0L);
            statusCounts.put("executing", 0L);
            statusCounts.put("completed", 0L);
            statusCounts.put("cancelled", 0L);
        }

        return statusCounts;
    }

    @Override
    public EmployerOrderDetailRespVO getOrderDetail(String orderId) {
        log.info("获取雇主订单详情 - orderId: {}", orderId);

        try {
            // 1. 参数校验
            if (StrUtil.isEmpty(orderId)) {
                throw new ServiceException(400, "订单ID不能为空");
            }

            // 2. 查询订单主表信息
            PublicbizOrderDO orderDO = orderMapper.selectById(Long.valueOf(orderId));
            if (orderDO == null) {
                throw new ServiceException(404, "订单不存在");
            }

            // 3. 验证订单类型
            if (!"domestic".equals(orderDO.getOrderType())) {
                throw new ServiceException(400, "订单类型不正确");
            }

            // 4. 查询家政订单详情
            DomesticOrderDO domesticOrderDO = domesticOrderMapper.selectByOrderId(orderDO.getId());
            if (domesticOrderDO == null) {
                throw new ServiceException(404, "订单详情不存在");
            }

            // 5. 构建响应结果
            EmployerOrderDetailRespVO result = buildOrderDetailResponse(orderDO, domesticOrderDO);

            log.info("获取雇主订单详情成功 - orderId: {}", orderId);
            return result;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取雇主订单详情失败 - orderId: {}, 错误: {}",
                    orderId, e.getMessage(), e);
            throw new ServiceException(500, "获取订单详情失败，请稍后重试");
        }
    }

    /**
     * 构建订单详情响应
     */
    private EmployerOrderDetailRespVO buildOrderDetailResponse(PublicbizOrderDO orderDO,
            DomesticOrderDO domesticOrderDO) {
        EmployerOrderDetailRespVO result = new EmployerOrderDetailRespVO();

        // 设置基础订单信息
        result.setOrderId(orderDO.getId());
        result.setOrderNo(orderDO.getOrderNo());
        result.setOrderType(orderDO.getOrderType());
        result.setBusinessLine(orderDO.getBusinessLine());
        result.setOrderStatus(orderDO.getOrderStatus());
        result.setPaymentStatus(orderDO.getPaymentStatus());
        result.setTotalAmount(orderDO.getTotalAmount() != null ? orderDO.getTotalAmount().toString() : "0.00");
        result.setPaidAmount(orderDO.getPaidAmount() != null ? orderDO.getPaidAmount().toString() : "0.00");
        result.setRefundAmount(orderDO.getRefundAmount() != null ? orderDO.getRefundAmount().toString() : "0.00");
        result.setCreateTime(orderDO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        result.setUpdateTime(orderDO.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        result.setRemarks(orderDO.getRemark());

        // 构建家政订单详情
        EmployerOrderDetailRespVO.DomesticOrderDetail domesticOrderDetail = buildDomesticOrderDetail(domesticOrderDO);
        result.setDomesticOrder(domesticOrderDetail);

        // 设置服务地址信息
        result.setServiceAddress(domesticOrderDO.getServiceAddress());
        result.setServiceAddressDetail(domesticOrderDO.getServiceAddressDetail());

        // 构建进度步骤
        List<EmployerOrderDetailRespVO.ProgressStep> progressSteps = buildProgressSteps(orderDO);
        result.setProgressSteps(progressSteps);

        return result;
    }

    /**
     * 构建家政订单详情
     */
    private EmployerOrderDetailRespVO.DomesticOrderDetail buildDomesticOrderDetail(DomesticOrderDO domesticOrderDO) {
        EmployerOrderDetailRespVO.DomesticOrderDetail detail = new EmployerOrderDetailRespVO.DomesticOrderDetail();

        detail.setId(domesticOrderDO.getId());
        detail.setCustomerName(domesticOrderDO.getCustomerName());
        detail.setCustomerPhone(maskPhoneNumber(domesticOrderDO.getCustomerPhone()));
        detail.setServiceCategoryName(domesticOrderDO.getServiceCategoryName());
        detail.setServicePackageName(domesticOrderDO.getServicePackageName());
        detail.setServicePackageThumbnail(domesticOrderDO.getServicePackageThumbnail());
        detail.setServicePackagePrice(
                domesticOrderDO.getServicePackagePrice() != null ? domesticOrderDO.getServicePackagePrice().toString()
                        : "0.00");
        detail.setServicePackageOriginalPrice(domesticOrderDO.getServicePackageOriginalPrice() != null
                ? domesticOrderDO.getServicePackageOriginalPrice().toString()
                : "0.00");
        detail.setServicePackageUnit(domesticOrderDO.getServicePackageUnit());
        detail.setServicePackageDuration(domesticOrderDO.getServicePackageDuration());
        detail.setServicePackageType(domesticOrderDO.getServicePackageType());
        detail.setServiceTimes(domesticOrderDO.getServiceTimes());
        detail.setUnitPrice(
                domesticOrderDO.getUnitPrice() != null ? domesticOrderDO.getUnitPrice().toString() : "0.00");
        detail.setActualAmount(
                domesticOrderDO.getActualAmount() != null ? domesticOrderDO.getActualAmount().toString() : "0.00");

        // 服务人员信息
        detail.setPractitionerName(domesticOrderDO.getPractitionerName());
        detail.setPractitionerPhone(maskPhoneNumber(domesticOrderDO.getPractitionerPhone()));

        // 根据practitioner_oneid查询阿姨详细信息
        if (StrUtil.isNotEmpty(domesticOrderDO.getPractitionerOneid())) {
            PractitionerDO practitioner = practitionerMapper.selectByAuntOneId(domesticOrderDO.getPractitionerOneid());
            if (practitioner != null) {
                // 设置阿姨头像
                detail.setPractitionerAvatar(practitioner.getAvatar() != null ? practitioner.getAvatar() : "");

                // 设置阿姨标签（暂时返回空值）
                detail.setPractitionerBadge("");

                // 设置阿姨经验（根据experience_years字段计算）
                if (practitioner.getExperienceYears() != null) {
                    detail.setPractitionerExperience(practitioner.getExperienceYears() + "年经验");
                } else {
                    detail.setPractitionerExperience("");
                }

                // 统计阿姨服务家庭数（根据practitioner_oneid统计订单数量）
                int familyCount = getPractitionerFamilyCount(domesticOrderDO.getPractitionerOneid());
                detail.setPractitionerFamilies("服务" + familyCount + "个家庭");
            } else {
                // 如果查询不到阿姨信息，设置默认值
                detail.setPractitionerAvatar("");
                detail.setPractitionerBadge("");
                detail.setPractitionerExperience("");
                detail.setPractitionerFamilies("");
            }
        } else {
            // 如果没有practitioner_oneid，设置默认值
            detail.setPractitionerAvatar("");
            detail.setPractitionerBadge("");
            detail.setPractitionerExperience("");
            detail.setPractitionerFamilies("");
        }

        // 机构信息
        detail.setAgencyName(domesticOrderDO.getAgencyName());

        // 任务进度信息
        detail.setTaskCount(domesticOrderDO.getTaskCount());
        detail.setCompletedTaskCount(domesticOrderDO.getCompletedTaskCount());
        detail.setTaskProgress(
                domesticOrderDO.getTaskProgress() != null ? domesticOrderDO.getTaskProgress().toString() : "0.00");

        // 服务时间
        if (domesticOrderDO.getServiceStartDate() != null) {
            detail.setServiceStartDate(DateUtil.formatDate(domesticOrderDO.getServiceStartDate()));
        }
        if (domesticOrderDO.getServiceEndDate() != null) {
            detail.setServiceEndDate(DateUtil.formatDate(domesticOrderDO.getServiceEndDate()));
        }

        // 构建服务安排
        List<EmployerOrderDetailRespVO.ServiceSchedule> serviceSchedules = buildServiceSchedules(domesticOrderDO);
        detail.setServiceSchedule(serviceSchedules);

        return detail;
    }

    /**
     * 构建服务安排
     */
    private List<EmployerOrderDetailRespVO.ServiceSchedule> buildServiceSchedules(DomesticOrderDO domesticOrderDO) {
        List<EmployerOrderDetailRespVO.ServiceSchedule> schedules = new ArrayList<>();

        // 这里可以根据实际业务逻辑构建服务安排
        // 目前先返回空列表，后续可以根据工单表或其他业务表来构建
        // TODO: 根据实际业务需求完善服务安排的构建逻辑

        return schedules;
    }

    /**
     * 统计阿姨服务家庭数
     * 根据practitioner_oneid统计该服务人员存在的订单数量
     *
     * @param practitionerOneId 阿姨OneID
     * @return 服务家庭数
     */
    private int getPractitionerFamilyCount(String practitionerOneId) {
        try {
            if (StrUtil.isEmpty(practitionerOneId)) {
                return 0;
            }

            // 查询该阿姨的所有订单
            List<DomesticOrderDO> orders = domesticOrderMapper.selectByPractitionerOneid(practitionerOneId);
            if (orders == null || orders.isEmpty()) {
                return 0;
            }

            // 统计不同的客户数量（服务家庭数）
            return (int) orders.stream()
                    .map(DomesticOrderDO::getCustomerOneid)
                    .filter(StrUtil::isNotEmpty)
                    .distinct()
                    .count();

        } catch (Exception e) {
            log.error("统计阿姨服务家庭数失败 - practitionerOneId: {}, 错误: {}", practitionerOneId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 构建进度步骤
     */
    private List<EmployerOrderDetailRespVO.ProgressStep> buildProgressSteps(PublicbizOrderDO orderDO) {
        List<EmployerOrderDetailRespVO.ProgressStep> steps = new ArrayList<>();

        // 支付步骤
        EmployerOrderDetailRespVO.ProgressStep paymentStep = new EmployerOrderDetailRespVO.ProgressStep();
        paymentStep.setStep("payment");
        paymentStep.setStepName("已支付");
        if ("paid".equals(orderDO.getPaymentStatus()) || "refunded".equals(orderDO.getPaymentStatus())) {
            paymentStep.setStatus("completed");
            paymentStep.setCompletedTime(
                    orderDO.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else {
            paymentStep.setStatus("pending");
        }
        steps.add(paymentStep);

        // 接单步骤
        EmployerOrderDetailRespVO.ProgressStep acceptedStep = new EmployerOrderDetailRespVO.ProgressStep();
        acceptedStep.setStep("accepted");
        acceptedStep.setStepName("已接单");
        if ("executing".equals(orderDO.getOrderStatus()) || "completed".equals(orderDO.getOrderStatus())) {
            acceptedStep.setStatus("completed");
            acceptedStep.setCompletedTime(
                    orderDO.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else {
            acceptedStep.setStatus("pending");
        }
        steps.add(acceptedStep);

        // 服务中步骤
        EmployerOrderDetailRespVO.ProgressStep executingStep = new EmployerOrderDetailRespVO.ProgressStep();
        executingStep.setStep("executing");
        executingStep.setStepName("服务中");
        if ("executing".equals(orderDO.getOrderStatus())) {
            executingStep.setStatus("current");
            executingStep
                    .setStartedTime(orderDO.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else if ("completed".equals(orderDO.getOrderStatus())) {
            executingStep.setStatus("completed");
            executingStep.setCompletedTime(
                    orderDO.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else {
            executingStep.setStatus("pending");
        }
        steps.add(executingStep);

        // 评价步骤
        EmployerOrderDetailRespVO.ProgressStep evaluationStep = new EmployerOrderDetailRespVO.ProgressStep();
        evaluationStep.setStep("evaluation");
        evaluationStep.setStepName("待评价");
        if ("completed".equals(orderDO.getOrderStatus())) {
            evaluationStep.setStatus("pending");
        } else {
            evaluationStep.setStatus("pending");
        }
        steps.add(evaluationStep);

        return steps;
    }

    /**
     * 构建服务频次
     *
     * @param serviceIntervalType  服务间隔类型
     * @param serviceIntervalValue 服务间隔数值
     * @return 服务频次字符串
     */
    private String buildServiceFrequency(String serviceIntervalType, Integer serviceIntervalValue) {
        if (StrUtil.isEmpty(serviceIntervalType) || serviceIntervalValue == null) {
            return "";
        }

        String typeText;
        switch (serviceIntervalType.toLowerCase()) {
            case "day":
                typeText = "每天";
                break;
            case "weekly":
                typeText = "每周";
                break;
            case "monthly":
                typeText = "每月";
                break;
            case "year":
                typeText = "每年";
                break;
            default:
                log.warn("未知的服务间隔类型: {}", serviceIntervalType);
                return "";
        }

        return typeText + serviceIntervalValue + "次";
    }

    /**
     * 构建服务时间安排JSON
     *
     * @param createReqVO    创建订单请求
     * @param servicePackage 服务套餐信息
     * @return 服务时间安排JSON字符串
     */
    private String buildServiceSchedule(EmployerOrderCreateReqVO createReqVO, ServicePackageDO servicePackage) {
        try {
            String packageType = servicePackage.getPackageType();
            log.info("构建服务时间安排JSON - 套餐类型: {}, 套餐ID: {}", packageType, servicePackage.getId());

            // 优先处理服务时间列表，如果有的话
            List<String> serviceTimes = createReqVO.getServiceTimes();
            if (serviceTimes != null && !serviceTimes.isEmpty()) {
                String result = JSONUtil.toJsonStr(serviceTimes);
                log.info("服务时间安排JSON: {}", result);
                return result;
            }

            // 如果没有服务时间列表，根据套餐类型处理
            if ("count-card".equals(packageType)) {
                // 次卡次数套餐：没有服务时间列表时返回空数组
                log.warn("次卡套餐但未提供服务时间列表");
                return "[]";
            } else if ("long-term".equals(packageType)) {
                // 长周期套餐：存储服务开始日期
                String startDate = createReqVO.getStartDate();
                if (StrUtil.isNotEmpty(startDate)) {
                    String result = JSONUtil.toJsonStr(startDate);
                    log.info("长周期套餐服务时间安排JSON: {}", result);
                    return result;
                } else {
                    log.warn("长周期套餐但未提供服务开始日期");
                    return "{}";
                }
            } else {
                log.warn("未知的套餐类型: {}", packageType);
                return "{}";
            }
        } catch (Exception e) {
            log.error("构建服务时间安排JSON失败", e);
            return "{}";
        }
    }

    @Override
    public PackagesAndAuntInfoRespVO getPackagesAndAuntInfo(Long orderId) {
        log.info("根据订单号获取套餐和阿姨信息 - orderId: {}", orderId);

        try {
            // 1. 参数校验
            if (orderId == null) {
                throw new ServiceException(400, "订单ID不能为空");
            }

            // 2. 查询订单主表信息
            PublicbizOrderDO orderDO = orderMapper.selectById(orderId);
            if (orderDO == null) {
                throw new ServiceException(404, "订单不存在");
            }

            // 3. 验证订单类型
            if (!"domestic".equals(orderDO.getOrderType())) {
                throw new ServiceException(400, "订单类型不正确");
            }

            // 4. 查询家政订单详情
            DomesticOrderDO domesticOrderDO = domesticOrderMapper.selectByOrderId(orderDO.getId());
            if (domesticOrderDO == null) {
                throw new ServiceException(404, "订单详情不存在");
            }

            // 5. 查询阿姨订单任务表，获取阿姨信息
            List<DomesticTaskDO> tasks = domesticTaskMapper.selectByOrderIdOrderByTaskSequence(orderId);
            String auntOneId = null;
            String auntName = null;

            if (tasks != null && !tasks.isEmpty()) {
                // 根据task_sequence升序排序，取最后一个任务的服务人员信息
                DomesticTaskDO lastTask = tasks.get(tasks.size() - 1);
                auntOneId = lastTask.getPractitionerOneid();
                auntName = lastTask.getPractitionerName();
            }

            // 6. 构建响应结果
            PackagesAndAuntInfoRespVO result = new PackagesAndAuntInfoRespVO();
            result.setPackageMainImage(domesticOrderDO.getServicePackageThumbnail());
            result.setPackageName(domesticOrderDO.getServicePackageName());
            result.setAuntName(auntName);
            result.setAuntOneId(auntOneId);
            result.setServicePackageId(String.valueOf(domesticOrderDO.getServicePackageId()));
            result.setAgencyId(String.valueOf(domesticOrderDO.getAgencyId()));
            result.setAgencyName(domesticOrderDO.getAgencyName());

            log.info("根据订单号获取套餐和阿姨信息成功 - orderId: {}, auntName: {}, auntOneId: {}",
                    orderId, auntName, auntOneId);
            return result;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("根据订单号获取套餐和阿姨信息失败 - orderId: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException(500, "获取套餐和阿姨信息失败，请稍后重试");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderReviewRespVO submitOrderReview(OrderReviewReqVO reviewReqVO) {
        log.info("提交订单评价 - reviewReqVO: {}", reviewReqVO);

        try {
            // 1. 参数校验
            validateReviewRequest(reviewReqVO);

            // 2. 获取当前登录用户信息
            String currentUserId = getCurrentUserId();
            if (currentUserId == null) {
                throw new ServiceException(401, "用户未登录或登录状态已过期");
            }

            MpUserDO currentUser = mpUserMapper.selectById(Long.valueOf(currentUserId));
            if (currentUser == null) {
                throw new ServiceException(404, "用户信息不存在");
            }

            // 3. 验证订单信息
            PublicbizOrderDO orderDO = validateOrderForReview(reviewReqVO.getOrderId(), currentUserId);

            // 4. 检查是否已评价
            AuntReviewDO existingReview = auntReviewMapper.selectByOrderId(reviewReqVO.getOrderId());
            if (existingReview != null) {
                throw new ServiceException(400, "该订单已评价，不能重复评价");
            }

            // 5. 处理图片上传
            log.info("开始处理评价图片 - 原始图片列表: {}", reviewReqVO.getReviewImages());
            log.info("原始图片列表类型: {}",
                    reviewReqVO.getReviewImages() != null ? reviewReqVO.getReviewImages().getClass().getName()
                            : "null");
            log.info("原始图片列表大小: {}", reviewReqVO.getReviewImages() != null ? reviewReqVO.getReviewImages().size() : 0);

            // 测试图片处理逻辑
            testImageProcessing(reviewReqVO.getReviewImages());

            List<String> reviewImageUrls = processReviewImages(reviewReqVO.getReviewImages());
            log.info("评价图片处理完成 - 处理后图片列表: {}", reviewImageUrls);

            // 6. 创建评价记录
            AuntReviewDO reviewDO = createReviewRecord(reviewReqVO, currentUser, orderDO, reviewImageUrls);

            // 7. 构建响应结果
            OrderReviewRespVO result = buildReviewResponse(reviewDO);

            log.info("订单评价提交成功 - reviewId: {}, orderId: {}, 存储的图片JSON: {}",
                    reviewDO.getId(), reviewReqVO.getOrderId(), reviewDO.getReviewImages());
            return result;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("提交订单评价失败 - reviewReqVO: {}, 错误: {}", reviewReqVO, e.getMessage(), e);
            throw new ServiceException(500, "提交评价失败，请稍后重试");
        }
    }

    /**
     * 验证评价请求参数
     */
    private void validateReviewRequest(OrderReviewReqVO reviewReqVO) {
        if (reviewReqVO.getOrderId() == null) {
            throw new ServiceException(400, "订单ID不能为空");
        }
        if (StrUtil.isEmpty(reviewReqVO.getAuntOneId())) {
            throw new ServiceException(400, "阿姨OneId不能为空");
        }
        if (reviewReqVO.getAttitudeRating() == null || reviewReqVO.getAttitudeRating().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(400, "服务态度评分不能为空且不能小于0");
        }
        if (reviewReqVO.getProfessionalismRating() == null
                || reviewReqVO.getProfessionalismRating().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(400, "技术专业性评分不能为空且不能小于0");
        }
        if (reviewReqVO.getResponsibilityRating() == null
                || reviewReqVO.getResponsibilityRating().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(400, "责任心评分不能为空且不能小于0");
        }
        if (StrUtil.isEmpty(reviewReqVO.getReviewContent())) {
            throw new ServiceException(400, "评价内容不能为空");
        }
        if (reviewReqVO.getReviewContent().length() > 500) {
            throw new ServiceException(400, "评价内容不能超过500字符");
        }
    }

    /**
     * 获取当前登录用户ID
     */
    private String getCurrentUserId() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        return userId != null ? userId.toString() : null;
    }

    /**
     * 验证订单信息
     */
    private PublicbizOrderDO validateOrderForReview(Long orderId, String currentUserId) {
        // 1. 查询订单主表信息
        PublicbizOrderDO orderDO = orderMapper.selectById(orderId);
        if (orderDO == null) {
            throw new ServiceException(404, "订单不存在");
        }

        // 2. 验证订单是否属于当前用户
        // if (!currentUserId.equals(orderDO.getCreator())) {
        // throw new ServiceException(403, "只能评价自己的订单");
        // }

        // 3. 验证订单状态是否为已完成
        if (!"completed".equals(orderDO.getOrderStatus())) {
            throw new ServiceException(400, "只能评价已完成的订单");
        }

        // 4. 验证订单类型
        if (!"domestic".equals(orderDO.getOrderType())) {
            throw new ServiceException(400, "订单类型不正确");
        }

        // 5. 验证评价时间（订单完成后30天内）
        // LocalDateTime orderCompleteTime = orderDO.getUpdateTime();
        // LocalDateTime now = LocalDateTime.now();
        // if (orderCompleteTime.plusDays(30).isBefore(now)) {
        // throw new ServiceException(400, "订单完成后30天内才能评价");
        // }

        return orderDO;
    }

    /**
     * 处理评价图片列表
     * 支持base64格式和URL格式的图片
     */
    private List<String> processReviewImages(List<String> reviewImages) {
        List<String> imageUrls = new ArrayList<>();

        if (reviewImages != null && !reviewImages.isEmpty()) {
            log.info("开始处理评价图片，原始图片列表: {}", reviewImages);

            for (int i = 0; i < reviewImages.size(); i++) {
                String imageData = reviewImages.get(i);
                try {
                    log.debug("处理第{}张图片: {}", i + 1, imageData);

                    if (imageData != null && !imageData.trim().isEmpty()) {
                        String trimmedData = imageData.trim();

                        // 检查是否为base64格式
                        if (isBase64Image(trimmedData)) {
                            // 如果是base64格式，先上传到文件服务器，然后获取URL
                            String imageUrl = uploadBase64Image(trimmedData, "review_images");
                            if (imageUrl != null) {
                                imageUrls.add(imageUrl);
                                log.debug("base64图片上传成功，URL: {}", imageUrl);
                            } else {
                                log.warn("base64图片上传失败 (索引: {})", i);
                            }
                        } else if (isValidImageUrl(trimmedData)) {
                            // 如果是有效的URL格式，直接使用
                            imageUrls.add(trimmedData);
                            log.debug("添加有效图片URL: {}", trimmedData);
                        } else {
                            log.warn("跳过无效的图片格式: {} (索引: {})", imageData, i);
                        }
                    } else {
                        log.warn("跳过空或null的图片数据 (索引: {})", i);
                    }
                } catch (Exception e) {
                    log.error("处理评价图片失败 (索引: {}): {}", i, e.getMessage(), e);
                    // 继续处理其他图片，不因为单张图片失败而中断整个评价
                }
            }
        } else {
            log.info("评价图片列表为空或null");
        }

        log.info("处理评价图片完成，原始数量: {}, 有效数量: {}, 图片列表: {}",
                reviewImages != null ? reviewImages.size() : 0, imageUrls.size(), imageUrls);
        return imageUrls;
    }

    /**
     * 判断是否为base64格式的图片
     */
    private boolean isBase64Image(String data) {
        if (data == null || data.trim().isEmpty()) {
            return false;
        }

        String trimmedData = data.trim();

        // 检查是否以data:image/开头
        if (trimmedData.startsWith("data:image/")) {
            return true;
        }

        // 检查是否为纯base64编码（不包含data:image/前缀）
        // base64编码通常只包含字母、数字、+、/、=字符
        if (trimmedData.matches("^[A-Za-z0-9+/]*={0,2}$")) {
            // 进一步检查长度是否合理（base64编码的图片通常比较长）
            return trimmedData.length() > 100;
        }

        return false;
    }

    /**
     * 判断是否为有效的图片URL
     */
    private boolean isValidImageUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }

        String trimmedUrl = url.trim();

        return trimmedUrl.startsWith("http://") ||
                trimmedUrl.startsWith("https://") ||
                trimmedUrl.startsWith("/") ||
                trimmedUrl.startsWith("blob:") ||
                trimmedUrl.startsWith("file://");
    }

    /**
     * 上传base64图片到文件服务器
     */
    private String uploadBase64Image(String base64Data, String directory) {
        try {
            // 如果包含data:image/前缀，需要提取纯base64数据
            String pureBase64 = base64Data;
            if (base64Data.contains(",")) {
                pureBase64 = base64Data.substring(base64Data.indexOf(",") + 1);
            }

            // 解码base64数据
            byte[] imageBytes = java.util.Base64.getDecoder().decode(pureBase64);

            // 生成文件名
            String fileName = "review_" + System.currentTimeMillis() + "_" +
                    java.util.UUID.randomUUID().toString().substring(0, 8) + ".jpg";

            // 上传文件
            String imageUrl = uploadFileToInfra(imageBytes, fileName, directory);

            log.info("base64图片上传成功 - 文件名: {}, URL: {}", fileName, imageUrl);
            return imageUrl;

        } catch (Exception e) {
            log.error("上传base64图片失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 创建评价记录
     */
    private AuntReviewDO createReviewRecord(OrderReviewReqVO reviewReqVO, MpUserDO currentUser,
            PublicbizOrderDO orderDO, List<String> reviewImageUrls) {
        // 查询家政订单详情获取机构信息
        DomesticOrderDO domesticOrderDO = domesticOrderMapper.selectByOrderId(orderDO.getId());

        // 根据auntOneId查询阿姨基础信息，获取主键ID
        Long auntId = getAuntIdByOneId(reviewReqVO.getAuntOneId());
        if (auntId == null) {
            // 如果没有找到阿姨信息，默认赋值为0
            auntId = 0L;
            log.warn("未找到阿姨信息，使用默认ID: 0, auntOneId: {}", reviewReqVO.getAuntOneId());
        }

        AuntReviewDO reviewDO = new AuntReviewDO();
        reviewDO.setOrderId(reviewReqVO.getOrderId());
        reviewDO.setAuntId(auntId.toString()); // 使用阿姨表的主键ID
        reviewDO.setReviewerId(Long.valueOf(currentUser.getId()));
        reviewDO.setReviewerName(reviewReqVO.getIsAnonymous() ? "匿名用户" : currentUser.getNickname());
        reviewDO.setReviewerAvatar(reviewReqVO.getIsAnonymous() ? null : currentUser.getHeadImageUrl());
        reviewDO.setAttitudeRating(reviewReqVO.getAttitudeRating());
        reviewDO.setProfessionalRating(reviewReqVO.getProfessionalismRating());
        reviewDO.setResponsibilityRating(reviewReqVO.getResponsibilityRating());
        // rating字段是自动计算的，不需要手动设置
        reviewDO.setReviewTags(JSONUtil.toJsonStr(reviewReqVO.getReviewTags()));
        reviewDO.setReviewContent(reviewReqVO.getReviewContent());

        // 处理评价图片URL，限制长度避免数据库截断
        log.info("开始处理评价图片JSON序列化 - 输入图片列表: {}", reviewImageUrls);
        String reviewImagesJson = processReviewImagesJson(reviewImageUrls);
        reviewDO.setReviewImages(reviewImagesJson);
        log.info("评价图片JSON序列化完成 - 最终JSON: {}", reviewImagesJson);

        reviewDO.setReviewType("service");
        reviewDO.setIsAnonymous(reviewReqVO.getIsAnonymous());
        reviewDO.setIsRecommend(reviewReqVO.getIsRecommend());
        reviewDO.setLikeCount(0);
        reviewDO.setStatus(true);
        reviewDO.setAgencyId(domesticOrderDO != null ? domesticOrderDO.getAgencyId() : null);
        reviewDO.setServicePackageId(reviewReqVO.getServicePackageId());
        reviewDO.setCreator(currentUser.getId().toString());
        reviewDO.setUpdater(currentUser.getId().toString());

        auntReviewMapper.insert(reviewDO);
        return reviewDO;
    }

    /**
     * 根据阿姨OneID查询阿姨主键ID
     *
     * @param auntOneId 阿姨OneID
     * @return 阿姨主键ID
     */
    private Long getAuntIdByOneId(String auntOneId) {
        try {
            PractitionerDO practitioner = practitionerMapper.selectByAuntOneId(auntOneId);
            if (practitioner == null) {
                log.warn("阿姨信息不存在，auntOneId: {}", auntOneId);
                return null;
            }
            return practitioner.getId();
        } catch (Exception e) {
            log.error("查询阿姨信息失败，auntOneId: {}", auntOneId, e);
            return null;
        }
    }

    /**
     * 处理评价图片URL的JSON序列化
     *
     * @param reviewImageUrls 图片URL列表
     * @return JSON字符串
     */
    private String processReviewImagesJson(List<String> reviewImageUrls) {
        try {
            if (reviewImageUrls == null || reviewImageUrls.isEmpty()) {
                log.info("评价图片URL列表为空，返回空数组JSON");
                return "[]";
            }

            // 过滤空值并限制数量为5张
            List<String> validUrls = reviewImageUrls.stream()
                    .limit(5)
                    .filter(url -> url != null && !url.trim().isEmpty())
                    .collect(Collectors.toList());

            String jsonResult = JSONUtil.toJsonStr(validUrls);

            log.info("处理评价图片URL JSON序列化完成，原始数量: {}, 有效数量: {}, JSON结果: {}, JSON长度: {}",
                    reviewImageUrls.size(), validUrls.size(), jsonResult, jsonResult.length());

            return jsonResult;

        } catch (Exception e) {
            log.error("处理评价图片URL JSON序列化失败", e);
            return "[]";
        }
    }

    /**
     * 构建评价响应
     */
    private OrderReviewRespVO buildReviewResponse(AuntReviewDO reviewDO) {
        OrderReviewRespVO result = new OrderReviewRespVO();
        result.setReviewId(reviewDO.getId());
        result.setOrderId(String.valueOf(reviewDO.getOrderId()));
        result.setAverageRating(reviewDO.getRating());
        result.setCreateTime(reviewDO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return result;
    }

    /**
     * 检查订单是否已评价
     *
     * @param orderId 订单ID
     * @return 是否已评价
     */
    private Boolean checkOrderReviewed(Long orderId) {
        try {
            AuntReviewDO review = auntReviewMapper.selectByOrderId(orderId);
            return review != null;
        } catch (Exception e) {
            log.error("查询订单评价状态失败，orderId: {}", orderId, e);
            return false;
        }
    }

    /**
     * 检查是否存在审批中的换人申请
     *
     * @param orderNo 订单号
     * @return 是否存在审批中的换人申请
     */
    private Boolean checkSubstitutionRequestInProgress(String orderNo) {
        try {
            if (StrUtil.isEmpty(orderNo)) {
                return false;
            }

            // 查询是否存在审批中的换人申请工单
            Boolean exists = workOrderMapper.existsSubstitutionRequestInProgress(orderNo);
            return exists != null ? exists : false;
        } catch (Exception e) {
            log.error("检查换人申请状态失败 - orderNo: {}, 错误: {}", orderNo, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 上传文件到infra模块（委托给FileUploadService）
     *
     * @param fileBytes 文件字节数组
     * @param fileName  文件名
     * @param directory 存储目录
     * @return 文件访问URL
     */
    private String uploadFileToInfra(byte[] fileBytes, String fileName, String directory) {
        return fileUploadService.uploadFile(fileBytes, fileName, directory);
    }

    /**
     * 测试图片处理逻辑（用于调试）
     */
    private void testImageProcessing(List<String> testImages) {
        log.info("=== 开始测试图片处理逻辑 ===");
        log.info("测试图片列表: {}", testImages);

        List<String> processedImages = processReviewImages(testImages);
        log.info("处理后的图片列表: {}", processedImages);

        String jsonResult = processReviewImagesJson(processedImages);
        log.info("JSON序列化结果: {}", jsonResult);

        log.info("=== 测试图片处理逻辑结束 ===");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReplaceRequestRespVO submitReplaceRequest(ReplaceRequestReqVO replaceReqVO) {
        log.info("提交换人申请请求，参数：{}", replaceReqVO);

        try {

            // 2. 获取当前登录用户ID
            String currentUserId = getCurrentUserId();

            // 3. 根据订单ID查询订单信息
            PublicbizOrderDO order = orderMapper.selectById(Long.valueOf(replaceReqVO.getOrderId()));
            if (order == null) {
                throw new ServiceException(404, "订单不存在");
            }

            // 4. 验证订单状态
            if (!"executing".equals(order.getOrderStatus())) {
                throw new ServiceException(423, "订单状态不允许换人申请，只有进行中的订单才能申请换人");
            }

            // 5. 检查是否已存在审批中的换人申请
            Boolean hasProgress = checkSubstitutionRequestInProgress(order.getOrderNo());
            if (hasProgress) {
                throw new ServiceException(409, "订单已提交过换人申请，请等待审批结果");
            }

            // 6. 获取正在进行中的任务信息
            DomesticTaskDO currentTask = domesticTaskMapper.selectInProgressTaskByOrderId(order.getId());
            if (currentTask == null) {
                throw new ServiceException(404, "未找到正在进行中的任务");
            }

            // 7. 生成工单编号
            String workOrderNo = generateWorkOrderNo();

            // 8. 构建工单内容
            String workOrderContent = buildWorkOrderContent(replaceReqVO);

            // 9. 创建工单记录
            WorkOrderDO workOrder = WorkOrderDO.builder()
                    .workOrderNo(workOrderNo)
                    .orderNo(order.getOrderNo())
                    .workOrderTitle("换人申请")
                    .workOrderContent(workOrderContent)
                    .workOrderType("substitution_request")
                    .priority("medium")
                    .workOrderStatus("pending")
                    .auntOneid(currentTask.getPractitionerOneid())
                    .auntName(currentTask.getPractitionerName())
                    .status(0) // 审批中
                    .complaintType(replaceReqVO.getReplaceReason())
                    .build();

            // 10. 保存工单
            workOrderMapper.insert(workOrder);

            // 11. 创建工单日志
            WorkOrderLogDO workOrderLog = WorkOrderLogDO.builder()
                    .workOrderNo(workOrderNo)
                    .logType("creation")
                    .logTitle("换人申请创建")
                    .logContent("雇主提交换人申请，原因：" + replaceReqVO.getReplaceReason() +
                            ("其他原因".equals(replaceReqVO.getReplaceReason())
                                    && StrUtil.isNotBlank(replaceReqVO.getCustomReason())
                                            ? " - " + replaceReqVO.getCustomReason()
                                            : ""))
                    .operatorId(Long.valueOf(currentUserId))
                    .relatedPartyType("employer")
                    .relatedPartyName("雇主")
                    .build();

            workOrderLogMapper.insert(workOrderLog);

            // 12. 构建响应
            ReplaceRequestRespVO result = new ReplaceRequestRespVO();
            result.setReplaceId(workOrder.getId());
            result.setOrderId(order.getOrderNo());
            result.setReplaceReason(replaceReqVO.getReplaceReason());
            result.setCustomReason(replaceReqVO.getCustomReason());
            result.setStatus("pending");
            result.setCreateTime(workOrder.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            log.info("换人申请提交成功，工单编号：{}", workOrderNo);
            return result;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("提交换人申请失败 - 参数: {}, 错误: {}", replaceReqVO, e.getMessage(), e);
            throw new ServiceException(500, "提交换人申请失败，请稍后重试");
        }
    }

    /**
     * 生成工单编号
     */
    private String generateWorkOrderNo() {
        LocalDateTime now = LocalDateTime.now();
        String timestamp = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        long snowflakeId = IdUtil.getSnowflake().nextId() % 1000;
        return "WO" + timestamp + String.format("%03d", snowflakeId);
    }

    /**
     * 构建工单内容
     */
    private String buildWorkOrderContent(ReplaceRequestReqVO replaceReqVO) {
        StringBuilder content = new StringBuilder();
        content.append("换人申请原因：").append(replaceReqVO.getReplaceReason());

        // 如果是"其他原因"，则拼接自定义原因
        if ("其他原因".equals(replaceReqVO.getReplaceReason()) && StrUtil.isNotBlank(replaceReqVO.getCustomReason())) {
            content.append(" - ").append(replaceReqVO.getCustomReason());
        }

        return content.toString();
    }

    @Override
    public InProgressOrderRespVO getInProgressOrders(String currentUserOneId) {
        log.info("获取用户进行中订单，用户OneId: {}", currentUserOneId);

        try {
            // 1. 根据用户OneId查询订单主表，获取该用户创建的所有家政服务订单
            List<PublicbizOrderDO> orders = orderMapper.selectByCreatorAndType(currentUserOneId, "domestic");

            if (orders == null || orders.isEmpty()) {
                log.info("用户 {} 没有家政服务订单", currentUserOneId);
                return buildEmptyResponse();
            }

            // 2. 获取所有订单ID
            List<Long> orderIds = orders.stream()
                    .map(PublicbizOrderDO::getId)
                    .collect(Collectors.toList());

            // 3. 查询这些订单的明细信息
            List<DomesticOrderDO> domesticOrders = domesticOrderMapper.selectByOrderIds(orderIds);
            Map<Long, DomesticOrderDO> domesticOrderMap = domesticOrders.stream()
                    .collect(Collectors.toMap(DomesticOrderDO::getOrderId, order -> order));

            // 4. 查询这些订单的进行中任务
            List<DomesticTaskDO> inProgressTasks = domesticTaskMapper.selectInProgressTasksByOrderIds(orderIds);

            if (inProgressTasks == null || inProgressTasks.isEmpty()) {
                log.info("用户 {} 的订单中没有进行中的任务", currentUserOneId);
                return buildEmptyResponse();
            }

            // 5. 构建响应数据
            List<InProgressOrderRespVO.InProgressOrderInfo> orderInfoList = new ArrayList<>();

            for (DomesticTaskDO task : inProgressTasks) {
                PublicbizOrderDO order = orders.stream()
                        .filter(o -> o.getId().equals(task.getOrderId()))
                        .findFirst()
                        .orElse(null);

                DomesticOrderDO domesticOrder = domesticOrderMap.get(task.getOrderId());

                if (order != null && domesticOrder != null) {
                    InProgressOrderRespVO.InProgressOrderInfo orderInfo = new InProgressOrderRespVO.InProgressOrderInfo();
                    orderInfo.setOrderNo(order.getOrderNo());
                    orderInfo.setOrderId(order.getId());
                    orderInfo.setServicePackageId(domesticOrder.getServicePackageId());
                    orderInfo.setServicePackageName(domesticOrder.getServicePackageName());

                    // 格式化计划开始时间
                    if (task.getPlannedStartTime() != null) {
                        orderInfo.setPlannedStartTime(
                                task.getPlannedStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }

                    orderInfoList.add(orderInfo);
                }
            }

            // 6. 按计划开始时间排序
            orderInfoList.sort((o1, o2) -> {
                if (o1.getPlannedStartTime() == null)
                    return 1;
                if (o2.getPlannedStartTime() == null)
                    return -1;
                return o1.getPlannedStartTime().compareTo(o2.getPlannedStartTime());
            });

            // 7. 构建响应
            InProgressOrderRespVO result = new InProgressOrderRespVO();
            result.setData(orderInfoList);

            log.info("获取用户进行中订单成功，用户OneId: {}, 订单数量: {}", currentUserOneId, orderInfoList.size());
            return result;

        } catch (Exception e) {
            log.error("获取用户进行中订单失败 - 用户OneId: {}, 错误: {}", currentUserOneId, e.getMessage(), e);
            throw new ServiceException(500, "获取进行中订单失败，请稍后重试");
        }
    }

    /**
     * 构建空响应
     */
    private InProgressOrderRespVO buildEmptyResponse() {
        InProgressOrderRespVO result = new InProgressOrderRespVO();
        result.setData(new ArrayList<>());
        return result;
    }

    /**
     * 根据服务时间拆分子任务单
     */
    private void createSubTasks(PublicbizOrderDO orderDO, DomesticOrderDO domesticOrderDO,
            EmployerOrderCreateReqVO createReqVO, ServicePackageDO servicePackage,
            String userOneId) {
        log.info("开始拆分子任务单 - 订单ID: {}, 订单号: {}", orderDO.getId(), orderDO.getOrderNo());

        try {
            // 1. 获取服务时间列表并校验
            List<String> serviceTimes = createReqVO.getServiceTimes();
            if (serviceTimes == null || serviceTimes.isEmpty()) {
                log.error("服务时间列表为空，无法创建订单 - 订单ID: {}", orderDO.getId());
                throw new ServiceException(400, "请至少选择一个服务时间");
            }

            // 2. 按时间升序排序
            serviceTimes.sort(String::compareTo);

            // 3. 创建任务列表
            List<DomesticTaskDO> tasks = new ArrayList<>();
            for (int i = 0; i < serviceTimes.size(); i++) {
                String serviceTime = serviceTimes.get(i);
                DomesticTaskDO task = createTask(orderDO, domesticOrderDO, servicePackage, serviceTime, i + 1,
                        userOneId);
                tasks.add(task);
            }

            // 4. 批量插入任务
            for (DomesticTaskDO task : tasks) {
                domesticTaskMapper.insert(task);
                log.info("创建任务成功 - 任务编号: {}, 任务序号: {}, 计划开始时间: {}",
                        task.getTaskNo(), task.getTaskSequence(), task.getPlannedStartTime());
            }

            // 5. 更新家政订单的任务统计信息
            updateDomesticOrderTaskCount(domesticOrderDO.getId(), tasks.size());

            log.info("拆分子任务单完成 - 订单ID: {}, 创建任务数量: {}", orderDO.getId(), tasks.size());

        } catch (Exception e) {
            log.error("拆分子任务单失败 - 订单ID: {}, 错误: {}", orderDO.getId(), e.getMessage(), e);
            throw new ServiceException(500, "拆分子任务单失败，请稍后重试");
        }
    }

    /**
     * 创建单个任务
     */
    private DomesticTaskDO createTask(PublicbizOrderDO orderDO, DomesticOrderDO domesticOrderDO,
            ServicePackageDO servicePackage, String serviceTime,
            int taskSequence, String userOneId) {

        // 解析服务时间
        LocalDateTime plannedStartTime = parseServiceTime(serviceTime);
        LocalDate scheduleDate = plannedStartTime.toLocalDate();

        // 计算计划结束时间（根据套餐的单次服务时长）
        LocalDateTime plannedEndTime = calculatePlannedEndTime(plannedStartTime, servicePackage);

        // 生成任务编号
        String taskNo = generateTaskNo();

        // 创建任务对象
        DomesticTaskDO task = new DomesticTaskDO();

        // 基础信息
        task.setOrderId(orderDO.getId());
        task.setOrderNo(orderDO.getOrderNo());
        task.setDomesticOrderId(domesticOrderDO.getId());
        task.setTaskNo(taskNo);
        task.setTaskSequence(taskSequence);
        task.setTaskName(servicePackage.getName());
        task.setTaskDescription(servicePackage.getServiceDescription());
        task.setTaskType(servicePackage.getCategory());
        task.setTaskStatus("pending"); // 待分配

        // 时间信息
        task.setPlannedStartTime(plannedStartTime);
        task.setPlannedEndTime(plannedEndTime);
        task.setScheduleDate(scheduleDate);
        task.setDuration(servicePackage.getSingleDurationHours() + "小时");

        // 服务信息
        task.setServiceCategoryId(servicePackage.getCategoryId());
        task.setServiceCategoryName(servicePackage.getCategory());

        // 客户信息
        Long customerId = getCustomerIdByOneId(domesticOrderDO.getCustomerOneid());
        task.setCustomerId(customerId);
        task.setCustomerName(domesticOrderDO.getCustomerName());
        task.setCustomerPhone(domesticOrderDO.getCustomerPhone());
        task.setServiceAddress(domesticOrderDO.getServiceAddress());

        // 服务人员信息（初始为空，由机构后续分配）
        task.setPractitionerOneid(null);
        task.setPractitionerName(null);
        task.setPractitionerPhone(null);

        // 审计信息
        task.setCreator(domesticOrderDO.getCustomerOneid());
        task.setCreateTime(LocalDateTime.now());
        task.setUpdater(domesticOrderDO.getCustomerOneid());
        task.setUpdateTime(LocalDateTime.now());
        task.setDeleted(false);

        return task;
    }

    /**
     * 解析服务时间字符串
     */
    private LocalDateTime parseServiceTime(String serviceTime) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
            return LocalDateTime.parse(serviceTime, formatter);
        } catch (Exception e) {
            log.error("解析服务时间失败 - serviceTime: {}, 错误: {}", serviceTime, e.getMessage());
            throw new ServiceException(400, "服务时间格式不正确，请使用 yyyy-MM-dd HH:mm 格式");
        }
    }

    /**
     * 计算计划结束时间
     */
    private LocalDateTime calculatePlannedEndTime(LocalDateTime plannedStartTime, ServicePackageDO servicePackage) {
        Integer singleDurationHours = servicePackage.getSingleDurationHours();
        if (singleDurationHours == null || singleDurationHours <= 0) {
            singleDurationHours = 2; // 默认2小时
        }
        return plannedStartTime.plusHours(singleDurationHours);
    }

    /**
     * 生成任务编号
     */
    private String generateTaskNo() {
        String dateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        long snowflakeId = IdUtil.getSnowflake().nextId();
        String suffix = String.format("%03d", snowflakeId % 1000);
        return "TASK" + dateTimeStr + suffix;
    }

    /**
     * 更新家政订单的任务统计信息
     */
    private void updateDomesticOrderTaskCount(Long domesticOrderId, int taskCount) {
        try {
            DomesticOrderDO updateDO = new DomesticOrderDO();
            updateDO.setId(domesticOrderId);
            updateDO.setTaskCount(taskCount);
            updateDO.setCompletedTaskCount(0);
            updateDO.setTaskProgress(BigDecimal.ZERO);
            updateDO.setUpdateTime(new Date());

            domesticOrderMapper.updateById(updateDO);
            log.info("更新家政订单任务统计信息成功 - 订单ID: {}, 任务数量: {}", domesticOrderId, taskCount);
        } catch (Exception e) {
            log.error("更新家政订单任务统计信息失败 - 订单ID: {}, 错误: {}", domesticOrderId, e.getMessage(), e);
        }
    }

    /**
     * 根据OneID获取客户主键ID
     */
    private Long getCustomerIdByOneId(String customerOneId) {
        if (StrUtil.isEmpty(customerOneId)) {
            log.warn("客户OneID为空，无法获取客户ID");
            return null;
        }

        try {
            // 根据OneID查询mpUser表获取主键ID
            MpUserDO mpUser = mpUserMapper.selectByOneId(customerOneId);
            if (mpUser == null) {
                log.warn("根据OneID未找到客户信息 - customerOneId: {}", customerOneId);
                return null;
            }

            log.info("根据OneID获取客户ID成功 - customerOneId: {}, customerId: {}", customerOneId, mpUser.getId());
            return mpUser.getId();
        } catch (Exception e) {
            log.error("根据OneID获取客户ID失败 - customerOneId: {}, 错误: {}", customerOneId, e.getMessage(), e);
            return null;
        }
    }

}

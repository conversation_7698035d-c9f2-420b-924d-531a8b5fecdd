package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 操作日志列表 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 操作日志列表 Response VO")
@Data
public class OptLogListRespVO {

    @Schema(description = "日志ID", example = "1")
    private Long id;

    @Schema(description = "日志类型", example = "order_create")
    private String logType;

    @Schema(description = "日志标题", example = "订单创建")
    private String logTitle;

    @Schema(description = "日志内容", example = "创建个人培训订单")
    private String logContent;

    @Schema(description = "旧状态", example = "")
    private String oldStatus;

    @Schema(description = "新状态", example = "draft")
    private String newStatus;

    @Schema(description = "操作人姓名", example = "系统管理员")
    private String operatorName;

    @Schema(description = "操作人角色", example = "admin")
    private String operatorRole;

    @Schema(description = "创建时间", example = "2024-06-01 10:00:00")
    private String createTime;
}




package cn.bztmaster.cnt.module.publicbiz.service.auth;

import cn.bztmaster.cnt.module.publicbiz.controller.app.auth.vo.*;

/**
 * 雇主端授权登录 Service 接口
 *
 * <AUTHOR>
 */
public interface EmployerAuthService {

    /**
     * 微信小程序一键授权登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    EmployerAuthLoginRespVO weixinMiniAppLogin(EmployerAuthWeixinMiniAppLoginReqVO reqVO);

    /**
     * 手机验证码登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    EmployerAuthLoginRespVO smsLogin(EmployerAuthSmsLoginReqVO reqVO);

    /**
     * 发送手机验证码
     *
     * @param reqVO 发送信息
     */
    void sendSmsCode(EmployerAuthSmsSendReqVO reqVO);

    /**
     * 校验手机验证码
     *
     * @param reqVO 校验信息
     */
    void validateSmsCode(EmployerAuthSmsValidateReqVO reqVO);

    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 登录结果
     */
    EmployerAuthLoginRespVO refreshToken(String refreshToken);

    /**
     * 更新用户信息
     *
     * @param reqVO 更新信息
     * @return 更新结果
     */
    EmployerUserUpdateRespVO updateUserInfo(EmployerUserUpdateReqVO reqVO);

}
package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 支付更新 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 支付更新 Request VO")
@Data
public class PaymentUpdateReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "支付状态", example = "paid")
    private String paymentStatus;

    @Schema(description = "支付备注", example = "支付信息已更新")
    private String paymentRemark;
}




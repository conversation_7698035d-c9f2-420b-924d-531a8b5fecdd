package cn.bztmaster.cnt.module.publicbiz.service.agency.impl;

import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyScheduleAuntListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyScheduleDayTaskRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyScheduleMonthRespVO;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic.DomesticTaskMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper;
import cn.bztmaster.cnt.module.publicbiz.service.agency.AgencyScheduleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AgencyScheduleServiceImpl implements AgencyScheduleService {

    @Resource
    private PractitionerMapper practitionerMapper;
    @Resource
    private DomesticTaskMapper domesticTaskMapper;

    @Override
    public List<AgencyScheduleAuntListRespVO> getAgencyAunts(Long agencyId, String status, String keyword) {
        // 简化实现：复用 BaseMapperX 条件构造，避免重复 DO
        LambdaQueryWrapperX<PractitionerDO> wrapper = new LambdaQueryWrapperX<PractitionerDO>()
                .eq(PractitionerDO::getAgencyId, agencyId)
                .eq(PractitionerDO::getDeleted, false)
                .eq(PractitionerDO::getStatus, (status == null || status.isEmpty()) ? "active" : status);
        if (keyword != null && !keyword.isEmpty()) {
            wrapper.and(w -> w.like(PractitionerDO::getName, keyword)
                    .or().like(PractitionerDO::getPhone, keyword));
        }
        List<PractitionerDO> list = practitionerMapper.selectList(wrapper);
        return list.stream().map(p -> {
            AgencyScheduleAuntListRespVO vo = new AgencyScheduleAuntListRespVO();
            vo.setId(p.getId());
            vo.setAuntOneId(p.getAuntOneid());
            vo.setName(p.getName());
            vo.setPhone(maskPhone(p.getPhone()));
            vo.setAvatar(p.getAvatar());
            vo.setCurrentStatus(p.getCurrentStatus());
            vo.setPlatformStatus(p.getPlatformStatus());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public AgencyScheduleMonthRespVO getMonthSchedules(Long agencyId, Integer year, Integer month) {
        // 查机构下阿姨
        List<PractitionerDO> aunts = practitionerMapper.selectList(
                new LambdaQueryWrapperX<PractitionerDO>()
                        .eq(PractitionerDO::getAgencyId, agencyId)
                        .eq(PractitionerDO::getDeleted, false)
                        .eq(PractitionerDO::getStatus, "active")
        );

        // 构建ID映射（如需扩展）
        // Map<Long, PractitionerDO> idMap = aunts.stream().collect(Collectors.toMap(PractitionerDO::getId, a -> a));

        // 计算当月起止日期
        LocalDate start = LocalDate.of(year, month, 1);
        LocalDate end = start.withDayOfMonth(start.lengthOfMonth());

        // 按每位阿姨查询该月任务并聚合
        Map<Long, Map<Integer, Integer>> schedules = new HashMap<>();
        for (PractitionerDO a : aunts) {
            List<DomesticTaskDO> tasks = domesticTaskMapper.selectByAuntOneIdAndDateRange(a.getAuntOneid(), start, end);
            Map<Integer, Integer> dayCount = new HashMap<>();
            for (DomesticTaskDO t : tasks) {
                if ("cancelled".equalsIgnoreCase(t.getTaskStatus())) {
                    continue;
                }
                if (t.getScheduleDate() == null) continue;
                int day = t.getScheduleDate().getDayOfMonth();
                dayCount.put(day, dayCount.getOrDefault(day, 0) + 1);
            }
            if (!dayCount.isEmpty()) {
                schedules.put(a.getId(), dayCount);
            } else {
                schedules.put(a.getId(), Collections.emptyMap());
            }
        }

        AgencyScheduleMonthRespVO resp = new AgencyScheduleMonthRespVO();
        resp.setYear(year);
        resp.setMonth(month);
        resp.setAunts(aunts.stream().map(a -> {
            AgencyScheduleMonthRespVO.AuntSimpleVO v = new AgencyScheduleMonthRespVO.AuntSimpleVO();
            v.setId(a.getId());
            v.setAuntOneId(a.getAuntOneid());
            v.setName(a.getName());
            return v;
        }).collect(Collectors.toList()));
        resp.setSchedules(schedules);
        return resp;
    }

    @Override
    public List<AgencyScheduleDayTaskRespVO> getDayTasks(Long agencyId, Long auntId, LocalDate date) {
        PractitionerDO aunt = practitionerMapper.selectById(auntId);
        if (aunt == null || !Objects.equals(aunt.getAgencyId(), agencyId)) {
            return Collections.emptyList();
        }
        List<DomesticTaskDO> tasks = domesticTaskMapper.selectByAuntOneIdAndScheduleDate(aunt.getAuntOneid(), date);
        DateTimeFormatter timeFmt = DateTimeFormatter.ofPattern("HH:mm");
        return tasks.stream()
                .sorted(Comparator.comparing(DomesticTaskDO::getPlannedStartTime, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(DomesticTaskDO::getId))
                .map(t -> {
                    AgencyScheduleDayTaskRespVO vo = new AgencyScheduleDayTaskRespVO();
                    vo.setId(t.getId());
                    vo.setTaskNo(t.getTaskNo());
                    vo.setServiceType(firstNonBlank(t.getServiceCategoryName(), t.getTaskName()));
                    vo.setDate(t.getScheduleDate() != null ? t.getScheduleDate().toString() : null);
                    String time;
                    if (t.getPlannedStartTime() != null && t.getPlannedEndTime() != null) {
                        time = t.getPlannedStartTime().format(timeFmt) + "-" + t.getPlannedEndTime().format(timeFmt);
                    } else {
                        time = t.getDuration() != null ? t.getDuration().toString() : null;
                    }
                    vo.setTime(time);
                    vo.setDuration(t.getDuration() != null ? t.getDuration().toString() : null);
                    vo.setCustomer(t.getCustomerName());
                    vo.setCustomerPhone(maskPhone(t.getCustomerPhone()));
                    vo.setAddress(t.getServiceAddress());
                    vo.setTaskStatus(t.getTaskStatus());
                    return vo;
                })
                .collect(Collectors.toList());
    }

    private String maskPhone(String phone) {
        if (phone == null || phone.length() < 7) return phone;
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }

    private String firstNonBlank(String a, String b) {
        if (a != null && !a.isEmpty()) return a;
        return b;
    }
}



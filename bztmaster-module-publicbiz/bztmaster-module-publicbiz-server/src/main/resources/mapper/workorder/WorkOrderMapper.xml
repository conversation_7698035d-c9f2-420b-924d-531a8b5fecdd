<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.workorder.WorkOrderMapper">

    <!-- 统计阿姨指定年月范围内的请假天数 -->
    <select id="countLeaveDays" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(
            CASE 
                WHEN duration_hours &lt;= 4 THEN 0.5
                WHEN duration_hours > 4 THEN 1
                ELSE duration_days
            END
        ), 0) as total_leave_days
        FROM publicbiz_work_order
        WHERE aunt_oneid = #{auntOneId}
          AND work_order_type = 'take_leave'
          AND status = 1
          AND YEAR(start_time) = #{year}
          AND MONTH(start_time) = #{month}
          AND deleted = 0
    </select>

    <!-- 统计阿姨指定年月范围内的调休天数 -->
    <select id="countAdjustDays" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(
            CASE 
                WHEN duration_hours &lt;= 4 THEN 0.5
                WHEN duration_hours > 4 THEN 1
                ELSE duration_days
            END
        ), 0) as total_adjust_days
        FROM publicbiz_work_order
        WHERE aunt_oneid = #{auntOneId}
          AND work_order_type = 'leave_adjustment'
          AND status = 1
          AND YEAR(start_time) = #{year}
          AND MONTH(start_time) = #{month}
          AND deleted = 0
    </select>

    <!-- 根据阿姨OneID查询工单列表 -->
    <select id="selectByAuntOneId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderDO">
        SELECT *
        FROM publicbiz_work_order
        WHERE aunt_oneid = #{auntOneId}
          AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据阿姨OneID和工单类型查询工单列表 -->
    <select id="selectByAuntOneIdAndType" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderDO">
        SELECT *
        FROM publicbiz_work_order
        WHERE aunt_oneid = #{auntOneId}
          AND work_order_type in ('take_leave','leave_adjustment')
          AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据阿姨OneID、工单类型和状态查询工单列表 -->
    <select id="selectByAuntOneIdAndTypeAndStatus" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderDO">
        SELECT *
        FROM publicbiz_work_order
        WHERE aunt_oneid = #{auntOneId}
          AND work_order_type = #{workOrderType}
          AND status = #{status}
          AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据阿姨OneID、工单类型、状态和年月查询工单列表 -->
    <select id="selectByAuntOneIdAndTypeAndStatusAndYearMonth" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderDO">
        SELECT *
        FROM publicbiz_work_order
        WHERE aunt_oneid = #{auntOneId}
          AND work_order_type = #{workOrderType}
          AND status = #{status}
          AND YEAR(start_time) = #{year}
          AND MONTH(start_time) = #{month}
          AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据工单编号查询工单 -->
    <select id="selectByWorkOrderNo" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderDO">
        SELECT *
        FROM publicbiz_work_order
        WHERE work_order_no = #{workOrderNo}
          AND deleted = 0
    </select>

    <!-- 根据订单号查询工单列表 -->
    <select id="selectByOrderNo" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderDO">
        SELECT *
        FROM publicbiz_work_order
        WHERE order_no = #{orderNo}
          AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据机构ID查询工单列表 -->
    <select id="selectByAgencyId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderDO">
        SELECT *
        FROM publicbiz_work_order
        WHERE agency_id = #{agencyId}
          AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据机构ID统计总工单数量 -->
    <select id="selectTotalCountByAgencyId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM publicbiz_work_order
        WHERE agency_id = #{agencyId}
          AND deleted = 0
    </select>

    <!-- 根据机构ID和工单类型统计工单数量 -->
    <select id="selectCountByAgencyIdAndType" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM publicbiz_work_order
        WHERE agency_id = #{agencyId}
          AND work_order_type = #{workOrderType}
          AND deleted = 0
    </select>

    <!-- 根据机构ID和多种工单类型统计工单数量 -->
    <select id="selectCountByAgencyIdAndTypes" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM publicbiz_work_order
        WHERE agency_id = #{agencyId}
          AND work_order_type IN
          <foreach collection="workOrderTypes" item="type" open="(" separator="," close=")">
              #{type}
          </foreach>
          AND deleted = 0
    </select>

    <!-- 检查订单是否存在审批中的换人申请 -->
    <select id="existsSubstitutionRequestInProgress" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM publicbiz_work_order
        WHERE order_no = #{orderNo}
          AND work_order_type = 'substitution_request'
          AND status = 0
          AND deleted = 0
    </select>

</mapper>

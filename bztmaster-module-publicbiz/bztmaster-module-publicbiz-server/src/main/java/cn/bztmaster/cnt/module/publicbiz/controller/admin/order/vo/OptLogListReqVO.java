package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * 操作日志列表 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 操作日志列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OptLogListReqVO extends PageParam {

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "PT123456789")
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @Schema(description = "日志类型", example = "order_create")
    private String logType;

    @Schema(description = "操作人姓名", example = "系统管理员")
    private String operatorName;
}




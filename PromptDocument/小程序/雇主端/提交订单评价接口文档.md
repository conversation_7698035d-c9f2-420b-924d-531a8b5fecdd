# 提交订单评价接口文档

## 1. 接口概述

### 1.1 接口说明
提交订单评价，支持对服务阿姨进行多维度评分、标签选择、文字评价和图片上传。

### 1.2 接口地址
```
POST /publicbiz/employer/order/review
```

### 1.3 请求方式
POST

### 1.4 接口描述
- 支持多维度评分：服务态度、技术专业性、责任心
- 支持评价标签选择
- 支持文字评价内容
- 支持图片上传（最多5张）
- 支持匿名评价
- 支持推荐标记

## 2. 请求参数

### 2.1 请求头
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer Token，用户登录凭证 |
| Content-Type | string | 是 | application/json |

### 2.2 请求体参数

#### 2.2.1 基础参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | string | 是 | 订单ID |
| auntOneId | string | 是 | 阿姨OneId |
| servicePackageId | integer | 否 | 服务套餐ID |

#### 2.2.2 评分参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| attitudeRating | decimal | 是 | 服务态度评分：1.0-5.0 |
| professionalismRating | decimal | 是 | 技术专业性评分：1.0-5.0 |
| responsibilityRating | decimal | 是 | 责任心评分：1.0-5.0 |

#### 2.2.3 评价内容
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| reviewContent | string | 是 | 评价内容，最大500字符 |
| reviewTags | array | 否 | 评价标签列表 |
| reviewImages | array | 否 | 评价图片URL列表，最多5张 |

#### 2.2.4 其他参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| isAnonymous | boolean | 否 | 是否匿名评价，默认false |
| isRecommend | boolean | 否 | 是否推荐，默认false |

### 2.3 请求示例

```json
{
  "orderId": 2221,
  "OneId": "fa2182d0-768a-11f0-ae0c-00163e1f6cb5",
  "servicePackageId": 1001,
  "attitudeRating": 4.5,
  "professionalismRating": 5.0,
  "responsibilityRating": 4.0,
  "reviewContent": "阿姨服务很专业，态度也很好，工作认真负责，准时守信，非常满意！",
     "reviewTags": ["服务周到", "技术专业", "准时守信"],
  "reviewImages": ["https://example.com/image1.jpg"],
  "isAnonymous": false,
  "isRecommend": true
}
```

## 3. 响应参数

### 3.1 响应格式
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "reviewId": 1001,
    "orderId": "202501150001",
    "averageRating": 4.5,
    "createTime": "2025-01-15 10:30:00"
  }
}
```

### 3.2 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | integer | 响应状态码，0表示成功 |
| msg | string | 响应消息 |
| data | object | 响应数据 |
| data.reviewId | integer | 评价ID |
| data.averageRating | decimal | 平均评分 |
| data.createTime | string | 创建时间 |

## 4. 业务规则

### 4.1 评价条件验证
1. 用户必须已登录且身份为雇主
2. 订单必须存在且属于当前用户
3. 订单状态必须为已完成
4. 订单未进行过评价
5. 评价时间在订单完成后30天内

### 4.2 评分规则
1. 三个维度评分都必须提供
2. 评分范围：1.0-5.0，支持0.5分
3. 平均评分 = (服务态度 + 技术专业性 + 责任心) / 3

### 4.3 评价内容规则
1. 评价内容不能为空，最大500字符
2. 评价标签可选，最多选择8个
3. 评价图片可选，最多5张



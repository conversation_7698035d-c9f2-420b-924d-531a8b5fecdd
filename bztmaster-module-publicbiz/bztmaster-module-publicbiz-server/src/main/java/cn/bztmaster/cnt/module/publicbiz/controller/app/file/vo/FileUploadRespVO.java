package cn.bztmaster.cnt.module.publicbiz.controller.app.file.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户 App - 文件上传 Response VO")
@Data
public class FileUploadRespVO {

    @Schema(description = "文件名", example = "example.jpg")
    private String fileName;

    @Schema(description = "文件访问URL", example = "https://example.com/files/example.jpg")
    private String fileUrl;

    @Schema(description = "文件大小（字节）", example = "1024")
    private Long fileSize;

    @Schema(description = "存储目录", example = "avatar")
    private String directory;

    @Schema(description = "文件类型", example = "image/jpeg")
    private String contentType;
}

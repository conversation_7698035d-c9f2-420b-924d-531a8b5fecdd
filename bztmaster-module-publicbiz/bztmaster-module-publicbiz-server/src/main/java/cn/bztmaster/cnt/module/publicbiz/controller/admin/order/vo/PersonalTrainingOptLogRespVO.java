package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 个人培训与认证订单操作日志响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 个人培训与认证订单操作日志响应")
@Data
public class PersonalTrainingOptLogRespVO {

    @Schema(description = "日志ID", example = "1")
    private Long id;

    @Schema(description = "订单号", example = "PT202406001")
    private String orderNo;

    @Schema(description = "日志类型", example = "订单创建")
    private String logType;

    @Schema(description = "日志标题", example = "创建个人培训订单")
    private String logTitle;

    @Schema(description = "日志内容", example = "创建了订单号为PT202406001的个人培训订单")
    private String logContent;

    @Schema(description = "原状态", example = "")
    private String oldStatus;

    @Schema(description = "新状态", example = "draft")
    private String newStatus;

    @Schema(description = "操作人姓名", example = "张三")
    private String operatorName;

    @Schema(description = "操作人角色", example = "销售员")
    private String operatorRole;

    @Schema(description = "创建时间", example = "2024-06-20 10:00:00")
    private String createTime;
}


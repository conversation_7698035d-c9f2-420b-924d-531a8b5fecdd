package cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 家政服务任务 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DomesticTaskMapper extends BaseMapperX<DomesticTaskDO> {

        /**
         * 根据阿姨OneID和日期范围查询排班任务
         *
         * @param auntOneId 阿姨OneID
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @return 排班任务列表
         */
        List<DomesticTaskDO> selectByAuntOneIdAndDateRange(@Param("auntOneId") String auntOneId,
                        @Param("startDate") LocalDate startDate,
                        @Param("endDate") LocalDate endDate);

        /**
         * 根据阿姨OneID查询指定日期的排班任务
         *
         * @param auntOneId    阿姨OneID
         * @param scheduleDate 排班日期
         * @return 排班任务列表
         */
        List<DomesticTaskDO> selectByAuntOneIdAndScheduleDate(@Param("auntOneId") String auntOneId,
                        @Param("scheduleDate") LocalDate scheduleDate);

        /**
         * 根据阿姨OneID查询进行中的任务
         *
         * @param auntOneId 阿姨OneID
         * @return 进行中的任务列表
         */
        List<DomesticTaskDO> selectInProgressByAuntOneId(@Param("auntOneId") String auntOneId);

        /**
         * 查询阿姨今日排班数
         *
         * @param auntOneId    阿姨OneID
         * @param scheduleDate 排班日期
         * @return 今日排班任务数量
         */
        Integer selectTodayScheduleCount(@Param("auntOneId") String auntOneId,
                        @Param("scheduleDate") LocalDate scheduleDate);

        /**
         * 查询阿姨本月服务时长
         *
         * @param auntOneId 阿姨OneID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 本月服务时长（小时）
         */
        Integer selectMonthlyServiceHours(@Param("auntOneId") String auntOneId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 根据任务编号列表查询任务
         *
         * @param taskNoList 任务编号列表
         * @return 任务列表
         */
        List<DomesticTaskDO> selectByTaskNoList(@Param("taskNoList") List<String> taskNoList);

        /**
         * 根据任务编号查询任务
         *
         * @param taskNo 任务编号
         * @return 任务对象
         */
        DomesticTaskDO selectByTaskNo(@Param("taskNo") String taskNo);

        /**
         * 根据任务编号列表更新服务人员信息
         *
         * @param taskNoList        任务编号列表
         * @param practitionerOneid 服务人员OneID
         * @param practitionerName  服务人员姓名
         * @param practitionerPhone 服务人员电话
         * @return 更新记录数
         */
        int updatePractitionerByTaskNoList(@Param("taskNoList") List<String> taskNoList,
                        @Param("practitionerOneid") String practitionerOneid,
                        @Param("practitionerName") String practitionerName,
                        @Param("practitionerPhone") String practitionerPhone);

        /**
         * 根据订单ID查询任务列表，按task_sequence升序排序
         *
         * @param orderId 订单ID
         * @return 任务列表
         */
        List<DomesticTaskDO> selectByOrderIdOrderByTaskSequence(@Param("orderId") Long orderId);

        /**
         * 根据阿姨OneID和月份统计每日任务数量
         *
         * @param auntOneId 阿姨OneID
         * @param startDate 月份开始日期
         * @param endDate   月份结束日期
         * @return 每日任务统计结果
         */
        List<Map<String, Object>> selectDailyTaskStatsByAuntOneId(@Param("auntOneId") String auntOneId,
                        @Param("startDate") LocalDate startDate,
                        @Param("endDate") LocalDate endDate);

        /**
         * 根据订单ID查询正在进行中的任务
         *
         * @param orderId 订单ID
         * @return 正在进行中的任务
         */
        DomesticTaskDO selectInProgressTaskByOrderId(@Param("orderId") Long orderId);

        /**
         * 根据订单ID列表查询进行中的任务
         *
         * @param orderIds 订单ID列表
         * @return 进行中的任务列表
         */
        List<DomesticTaskDO> selectInProgressTasksByOrderIds(@Param("orderIds") List<Long> orderIds);
}
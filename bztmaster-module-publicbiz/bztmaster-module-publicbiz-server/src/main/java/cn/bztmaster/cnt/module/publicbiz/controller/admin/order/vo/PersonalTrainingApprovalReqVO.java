package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 个人培训与认证订单审批请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 个人培训与认证订单审批请求")
@Data
public class PersonalTrainingApprovalReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "审批ID", example = "1")
    private Long approvalId;

    @Schema(description = "审批类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "order_approval")
    @NotNull(message = "审批类型不能为空")
    private String approvalType;

    @Schema(description = "审批级别", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "审批级别不能为空")
    private Integer approvalLevel;

    @Schema(description = "审批人ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "审批人ID列表不能为空")
    private List<Long> approverIds;

    @Schema(description = "审批结果", example = "approved")
    private String approvalResult;

    @Schema(description = "审批意见", example = "同意此订单")
    private String approvalOpinion;

    @Schema(description = "下一级审批人ID列表")
    private List<Long> nextApproverIds;

    @Schema(description = "备注信息", example = "请审批此订单")
    private String remark;
}


### 测试 refresh-token 接口
POST {{appApi}}/publicbiz/employer/auth/refresh-token?refreshToken=your_refresh_token_here
Content-Type: application/json
tenant-id: {{appTenantId}}

### 测试微信小程序登录接口
POST {{appApi}}/publicbiz/employer/auth/weixin-mini-app-login
Content-Type: application/json
tenant-id: {{appTenantId}}

{
  "code": "test_code",
  "phoneCode": "test_phone_code",
  "loginCode": "test_login_code",
  "nickname": "测试用户",
  "headImageUrl": "https://example.com/avatar.jpg"
}

### 测试手机验证码登录接口
POST {{appApi}}/publicbiz/employer/auth/sms-login
Content-Type: application/json
tenant-id: {{appTenantId}}

{
  "mobile": "13800138000",
  "code": "123456"
}

### 测试发送验证码接口
POST {{appApi}}/publicbiz/employer/auth/send-sms-code
Content-Type: application/json
tenant-id: {{appTenantId}}

{
  "mobile": "13800138000"
}

### 测试校验验证码接口
POST {{appApi}}/publicbiz/employer/auth/validate-sms-code
Content-Type: application/json
tenant-id: {{appTenantId}}

{
  "mobile": "13800138000",
  "code": "123456"
}

### 测试更新用户信息接口
PUT {{appApi}}/publicbiz/employer/auth/user/update
Content-Type: application/json
Authorization: Bearer your_access_token_here
tenant-id: {{appTenantId}}

{
  "nickname": "新昵称",
  "avatar": "https://example.com/new_avatar.jpg"
} 
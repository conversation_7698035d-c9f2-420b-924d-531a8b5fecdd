package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 个人培训与认证订单审批分页请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 个人培训与认证订单审批分页请求")
@Data
public class PersonalTrainingApprovalPageReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "当前页码", example = "1")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer size = 10;

    @Schema(description = "审批类型筛选", example = "order_approval")
    private String approvalType;

    @Schema(description = "审批结果筛选", example = "approved")
    private String approvalResult;

    @Schema(description = "开始日期", example = "2024-06-01")
    private String startDate;

    @Schema(description = "结束日期", example = "2024-06-20")
    private String endDate;
}


package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 提交订单评价 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "提交订单评价 Response VO")
@Data
public class OrderReviewRespVO {

    @Schema(description = "评价ID", example = "1001")
    private Long reviewId;

    @Schema(description = "订单ID", example = "202501150001")
    private String orderId;

    @Schema(description = "平均评分", example = "4.5")
    private BigDecimal averageRating;

    @Schema(description = "创建时间", example = "2025-01-15 10:30:00")
    private String createTime;
}

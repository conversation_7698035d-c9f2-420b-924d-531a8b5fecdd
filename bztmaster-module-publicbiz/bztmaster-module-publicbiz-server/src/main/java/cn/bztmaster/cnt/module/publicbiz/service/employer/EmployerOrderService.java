package cn.bztmaster.cnt.module.publicbiz.service.employer;

import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderCreateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderCreateRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.PackagesAndAuntInfoRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.OrderReviewReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.OrderReviewRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ReplaceRequestReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ReplaceRequestRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.InProgressOrderRespVO;

import javax.validation.Valid;

/**
 * 雇主端订单 Service 接口
 *
 * <AUTHOR>
 */
public interface EmployerOrderService {

    /**
     * 获取雇主订单列表
     *
     * @param customerOpenId 客户OpenId
     * @param status         订单状态筛选
     * @param page           页码
     * @param size           每页数量
     * @return 订单列表
     */
    EmployerOrderListRespVO getOrderList(String customerOpenId, String status, Integer page, Integer size);

    /**
     * 获取雇主订单详情
     *
     * @param orderId 订单ID
     * @return 订单详情
     */
    EmployerOrderDetailRespVO getOrderDetail(String orderId);

    /**
     * 创建服务套餐订单
     *
     * @param createReqVO 创建订单请求
     * @return 创建订单响应
     */
    EmployerOrderCreateRespVO createOrder(@Valid EmployerOrderCreateReqVO createReqVO);

    /**
     * 根据订单号获取套餐和阿姨信息
     *
     * @param orderId 订单ID
     * @return 套餐和阿姨信息
     */
    PackagesAndAuntInfoRespVO getPackagesAndAuntInfo(Long orderId);

    /**
     * 提交订单评价
     *
     * @param reviewReqVO 评价请求
     * @return 评价响应
     */
    OrderReviewRespVO submitOrderReview(OrderReviewReqVO reviewReqVO);

    /**
     * 申请换人
     *
     * @param replaceReqVO 换人申请请求
     * @return 换人申请响应
     */
    ReplaceRequestRespVO submitReplaceRequest(ReplaceRequestReqVO replaceReqVO);

    /**
     * 获取当前用户进行中的订单
     *
     * @param currentUserOneId 当前用户OneId
     * @return 进行中订单列表
     */
    InProgressOrderRespVO getInProgressOrders(String currentUserOneId);
}

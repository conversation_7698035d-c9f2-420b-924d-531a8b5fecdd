package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 个人培训与认证订单更新 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 个人培训与认证订单更新 Request VO")
@Data
public class PersonalTrainingUpdateReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "订单ID不能为空")
    private Long id;

    @Schema(description = "学员姓名", example = "张三")
    private String studentName;

    @Schema(description = "学员电话", example = "13800138000")
    private String studentPhone;

    @Schema(description = "学员邮箱", example = "<EMAIL>")
    private String studentEmail;

    @Schema(description = "学员身份证号", example = "110101199001011234")
    private String studentIdCard;

    @Schema(description = "课程名称", example = "Java高级开发")
    private String courseName;

    @Schema(description = "课程描述", example = "Java高级开发课程，包含Spring Boot、微服务等内容")
    private String courseDescription;

    @Schema(description = "课程时长", example = "120课时")
    private String courseDuration;

    @Schema(description = "课程费", example = "5000.00")
    @DecimalMin(value = "0.01", message = "课程费必须大于0")
    private BigDecimal courseFee;

    @Schema(description = "考试费", example = "500.00")
    @DecimalMin(value = "0.00", message = "考试费不能小于0")
    private BigDecimal examFee;

    @Schema(description = "认证费", example = "300.00")
    @DecimalMin(value = "0.00", message = "认证费不能小于0")
    private BigDecimal certificationFee;

    @Schema(description = "关联商机ID", example = "OPP001")
    private String businessOpportunity;

    @Schema(description = "关联线索ID", example = "LEAD001")
    private String associatedLead;

    @Schema(description = "订单来源", example = "官网")
    private String orderSource;

    @Schema(description = "备注信息", example = "学员有Java基础，希望深入学习")
    private String remark;
}




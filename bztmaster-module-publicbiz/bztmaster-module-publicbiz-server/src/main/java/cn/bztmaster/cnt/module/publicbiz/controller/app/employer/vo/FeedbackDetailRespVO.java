package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 意见反馈详情 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "意见反馈详情 Response VO")
@Data
public class FeedbackDetailRespVO {

    @Schema(description = "反馈ID", example = "1001")
    private Long feedbackId;

    @Schema(description = "状态", example = "pending")
    private String status;

    @Schema(description = "投诉信息")
    private ComplaintInfo complaintInfo;

    @Schema(description = "服务信息")
    private ServiceInfo serviceInfo;

    @Schema(description = "投诉信息")
    @Data
    public static class ComplaintInfo {

        @Schema(description = "投诉类型列表", example = "[\"服务态度\", \"专业能力\"]")
        private List<String> complaintTypes;

        @Schema(description = "客户信息", example = "张女士 138****5678")
        private String customerInfo;

        @Schema(description = "订单ID", example = "TX20251297")
        private String orderId;

        @Schema(description = "投诉时间", example = "2025-04-09 14:09:03")
        private String complaintTime;

        @Schema(description = "投诉内容", example = "阿姨在清洁过程中态度敷衍,清洁效果不理想,我要换阿姨.")
        private String complaintContent;

        @Schema(description = "投诉图片列表", example = "[\"https://example.com/image1.jpg\"]")
        private List<String> complaintImages;
    }

    @Schema(description = "服务信息")
    @Data
    public static class ServiceInfo {

        @Schema(description = "服务类型", example = "日常保洁")
        private String serviceType;

        @Schema(description = "服务地址", example = "杨浦区莲花小区")
        private String serviceAddress;

        @Schema(description = "服务时间", example = "2025-12-19 14:00~17:00")
        private String serviceTime;

        @Schema(description = "服务人员", example = "李丽芳")
        private String servicePersonnel;
    }
}

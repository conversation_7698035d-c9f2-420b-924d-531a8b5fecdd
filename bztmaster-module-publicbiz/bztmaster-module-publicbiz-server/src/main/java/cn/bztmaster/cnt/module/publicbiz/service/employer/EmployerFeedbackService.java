package cn.bztmaster.cnt.module.publicbiz.service.employer;

import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.FeedbackCreateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.FeedbackCreateRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.FeedbackListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.FeedbackDetailRespVO;

import javax.validation.Valid;

/**
 * 雇主端意见反馈 Service 接口
 *
 * <AUTHOR>
 */
public interface EmployerFeedbackService {

    /**
     * 新增意见反馈
     *
     * @param createReqVO 创建反馈请求
     * @return 创建反馈响应
     */
    FeedbackCreateRespVO createFeedback(@Valid FeedbackCreateReqVO createReqVO);

    /**
     * 获取意见反馈列表
     *
     * @param page   页码
     * @param size   每页数量
     * @param status 状态筛选
     * @return 反馈列表
     */
    FeedbackListRespVO getFeedbackList(Integer page, Integer size, String status);

    /**
     * 获取意见反馈详情
     *
     * @param feedbackId 反馈ID
     * @return 反馈详情
     */
    FeedbackDetailRespVO getFeedbackDetail(Long feedbackId);
}

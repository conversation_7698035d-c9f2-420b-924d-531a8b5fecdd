package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AgencyScheduleAuntListRespVO {
    @Schema(description = "阿姨ID")
    private Long id;
    @Schema(description = "阿姨OneID")
    private String auntOneId;
    @Schema(description = "姓名")
    private String name;
    @Schema(description = "手机号(脱敏)")
    private String phone;
    @Schema(description = "头像")
    private String avatar;
    @Schema(description = "当前状态")
    private String currentStatus;
    @Schema(description = "平台状态")
    private String platformStatus;
}



<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.AuntReviewMapper">

    <!-- 根据套餐ID查询平均评分 -->
    <select id="selectAverageRatingByPackageId" resultType="java.math.BigDecimal">
        SELECT COALESCE(AVG((attitude_rating + professional_rating + responsibility_rating) / 3), 0)
        FROM publicbiz_aunt_review
        WHERE deleted = 0
            AND status = 1
            AND service_package_id = #{packageId}
    </select>

    <!-- 根据套餐ID列表查询平均评分 -->
    <select id="selectAverageRatingByPackageIds" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.AuntReviewDO">
        SELECT
            service_package_id as servicePackageId,
            COALESCE(AVG((attitude_rating + professional_rating + responsibility_rating) / 3), 0) as rating
        FROM publicbiz_aunt_review
        WHERE deleted = 0
            AND status = 1
            AND service_package_id IN
        <foreach collection="packageIds" item="packageId" open="(" separator="," close=")">
            #{packageId}
        </foreach>
        GROUP BY service_package_id
    </select>

    <!-- 根据机构ID查询该机构所有套餐的平均评分 -->
    <select id="selectAverageRatingByAgencyId" resultType="java.math.BigDecimal">
        SELECT COALESCE(AVG((ar.attitude_rating + ar.professional_rating + ar.responsibility_rating) / 3), 0)
        FROM publicbiz_aunt_review ar
        INNER JOIN publicbiz_service_package sp ON ar.service_package_id = sp.id
        WHERE ar.deleted = 0
            AND ar.status = 1
            AND sp.deleted = 0
            AND sp.status = 'active'
            AND sp.audit_status = 'approved'
            AND sp.agency_id = #{agencyId}
    </select>

    <!-- 根据套餐ID查询服务态度平均评分 -->
    <select id="selectAverageAttitudeRatingByPackageId" resultType="java.math.BigDecimal">
        SELECT COALESCE(AVG(attitude_rating), 0)
        FROM publicbiz_aunt_review
        WHERE deleted = 0
            AND status = 1
            AND service_package_id = #{packageId}
    </select>

    <!-- 根据套餐ID查询技术专业性平均评分 -->
    <select id="selectAverageProfessionalRatingByPackageId" resultType="java.math.BigDecimal">
        SELECT COALESCE(AVG(professional_rating), 0)
        FROM publicbiz_aunt_review
        WHERE deleted = 0
            AND status = 1
            AND service_package_id = #{packageId}
    </select>

    <!-- 根据套餐ID查询责任心平均评分 -->
    <select id="selectAverageResponsibilityRatingByPackageId" resultType="java.math.BigDecimal">
        SELECT COALESCE(AVG(responsibility_rating), 0)
        FROM publicbiz_aunt_review
        WHERE deleted = 0
            AND status = 1
            AND service_package_id = #{packageId}
    </select>

    <!-- 根据套餐ID查询三个维度的平均评分 -->
    <select id="selectAverageRatingsByPackageId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.AuntReviewDO">
        SELECT
            service_package_id as servicePackageId,
            COALESCE(AVG(attitude_rating), 0) as attitudeRating,
            COALESCE(AVG(professional_rating), 0) as professionalRating,
            COALESCE(AVG(responsibility_rating), 0) as responsibilityRating,
            COALESCE(AVG((attitude_rating + professional_rating + responsibility_rating) / 3), 0) as rating
        FROM publicbiz_aunt_review
        WHERE deleted = 0
            AND status = 1
            AND service_package_id = #{packageId}
        GROUP BY service_package_id
    </select>

    <!-- 近6个月：按创建时间的月份分组统计平均评分 -->
    <select id="selectAvgRatingGroupByCreateMonth" resultType="java.util.HashMap">
        SELECT DATE_FORMAT(create_time, '%Y-%m') AS month,
        COALESCE(AVG(rating), 0)         AS avgRating
        FROM publicbiz_aunt_review
        WHERE deleted = 0
        AND agency_id = #{agencyId}
        AND create_time BETWEEN #{beginTime} AND #{endTime}
        GROUP BY month
        ORDER BY month ASC
    </select>

    <!-- 根据订单ID查询评价 -->
    <select id="selectByOrderId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.AuntReviewDO">
        SELECT *
        FROM publicbiz_aunt_review
        WHERE order_id = #{orderId}
            AND deleted = 0
        LIMIT 1
    </select>

</mapper>

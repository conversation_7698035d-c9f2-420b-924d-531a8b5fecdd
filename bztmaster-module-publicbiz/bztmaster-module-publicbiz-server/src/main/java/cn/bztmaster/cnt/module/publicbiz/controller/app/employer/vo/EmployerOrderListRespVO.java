package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 雇主端订单列表 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "雇主端订单列表 Response VO")
@Data
public class EmployerOrderListRespVO {

    @Schema(description = "总记录数", example = "25")
    private Long total;

    @Schema(description = "总页数", example = "3")
    private Long pages;

    @Schema(description = "当前页码", example = "1")
    private Long current;

    @Schema(description = "每页数量", example = "10")
    private Long size;

    @Schema(description = "订单列表")
    private List<OrderInfo> records;

    @Schema(description = "各状态订单数量统计")
    private Map<String, Long> statusCounts;

    @Schema(description = "订单信息")
    @Data
    public static class OrderInfo {

        @Schema(description = "订单ID", example = "1")
        private Long id;

        @Schema(description = "订单编号", example = "DO202412010001")
        private String orderNo;

        @Schema(description = "订单类型", example = "domestic")
        private String orderType;

        @Schema(description = "业务线", example = "家政服务")
        private String businessLine;

        @Schema(description = "订单总金额", example = "340.00")
        private String totalAmount;

        @Schema(description = "已支付金额", example = "0.00")
        private String paidAmount;

        @Schema(description = "退款金额", example = "0.00")
        private String refundAmount;

        @Schema(description = "支付状态", example = "pending")
        private String paymentStatus;

        @Schema(description = "订单状态", example = "pending_payment")
        private String orderStatus;

        @Schema(description = "创建时间", example = "2024-12-01 10:30:00")
        private String createTime;

        @Schema(description = "更新时间", example = "2024-12-01 10:30:00")
        private String updateTime;

        @Schema(description = "是否已评价", example = "false")
        private Boolean isReviewed;

        @Schema(description = "是否存在审批中的换人申请", example = "false")
        private Boolean hasProgress;

        @Schema(description = "家政订单详情")
        private DomesticOrderInfo domesticOrder;
    }

    @Schema(description = "家政订单详情")
    @Data
    public static class DomesticOrderInfo {

        @Schema(description = "订单ID", example = "1")
        private Long id;

        @Schema(description = "客户姓名", example = "张女士")
        private String customerName;

        @Schema(description = "客户手机号", example = "138****8888")
        private String customerPhone;

        @Schema(description = "客户地址", example = "北京市朝阳区")
        private String customerAddress;

        @Schema(description = "服务分类名称", example = "日常保洁")
        private String serviceCategoryName;

        @Schema(description = "服务套餐名称", example = "4次日常保洁 (3小时)")
        private String servicePackageName;

        @Schema(description = "服务套餐缩略图", example = "https://example.com/image1.jpg")
        private String servicePackageThumbnail;

        @Schema(description = "服务套餐价格", example = "340.00")
        private String servicePackagePrice;

        @Schema(description = "服务套餐原价", example = "400.00")
        private String servicePackageOriginalPrice;

        @Schema(description = "服务套餐单位", example = "次")
        private String servicePackageUnit;

        @Schema(description = "服务套餐时长", example = "3小时")
        private String servicePackageDuration;

        @Schema(description = "服务套餐类型", example = "count-card")
        private String servicePackageType;

        @Schema(description = "服务次数", example = "4")
        private Integer serviceTimes;

        @Schema(description = "单价", example = "85.00")
        private String unitPrice;

        @Schema(description = "实际金额", example = "340.00")
        private String actualAmount;

        @Schema(description = "服务地址", example = "北京市朝阳区某某小区")
        private String serviceAddress;

        @Schema(description = "服务地址详情", example = "1号楼1单元101室")
        private String serviceAddressDetail;

        @Schema(description = "服务人员姓名", example = "王阿姨")
        private String practitionerName;

        @Schema(description = "服务人员手机号", example = "139****9999")
        private String practitionerPhone;

        @Schema(description = "机构名称", example = "金牌家政")
        private String agencyName;

        @Schema(description = "任务总数", example = "4")
        private Integer taskCount;

        @Schema(description = "已完成任务数", example = "0")
        private Integer completedTaskCount;

        @Schema(description = "任务进度", example = "0.00")
        private String taskProgress;

        @Schema(description = "服务开始日期", example = "2024-12-02")
        private String serviceStartDate;

        @Schema(description = "服务结束日期", example = "2024-12-31")
        private String serviceEndDate;
    }
}

package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 合同信息 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 合同信息 Response VO")
@Data
public class ContractInfoRespVO {

    @Schema(description = "合同ID", example = "1")
    private Long contractId;

    @Schema(description = "合同类型", example = "electronic")
    private String contractType;

    @Schema(description = "合同文件URL", example = "https://example.com/contracts/1.pdf")
    private String contractFileUrl;

    @Schema(description = "合同状态", example = "signed")
    private String contractStatus;

    @Schema(description = "签署人", example = "学员本人")
    private String signer;

    @Schema(description = "签署日期", example = "2024-06-01")
    private String signDate;

    @Schema(description = "创建时间", example = "2024-06-01 10:00:00")
    private String createTime;

    @Schema(description = "更新时间", example = "2024-06-01 10:00:00")
    private String updateTime;
}




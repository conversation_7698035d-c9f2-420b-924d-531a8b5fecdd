package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 个人培训订单收款响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 个人培训订单收款响应 VO")
@Data
public class PersonalTrainingCollectionRespVO {

    @Schema(description = "支付记录ID", example = "1")
    private Long paymentId;

    @Schema(description = "订单ID", example = "1")
    private Long orderId;

    @Schema(description = "订单号", example = "PT1234567890")
    private String orderNo;

    @Schema(description = "支付单号", example = "PAY20241201001")
    private String paymentNo;

    @Schema(description = "支付类型", example = "cash")
    private String paymentType;

    @Schema(description = "支付类型名称", example = "现金")
    private String paymentTypeName;

    @Schema(description = "支付金额", example = "5000.00")
    private BigDecimal paymentAmount;

    @Schema(description = "支付状态", example = "success")
    private String paymentStatus;

    @Schema(description = "支付状态名称", example = "支付成功")
    private String paymentStatusName;

    @Schema(description = "支付时间", example = "2024-12-01 10:00:00")
    private LocalDateTime paymentTime;

    @Schema(description = "操作人ID", example = "1")
    private Long operatorId;

    @Schema(description = "操作人姓名", example = "张三")
    private String operatorName;

    @Schema(description = "支付备注", example = "现金收款")
    private String paymentRemark;

    @Schema(description = "第三方交易号", example = "TX123456789")
    private String transactionId;

    @Schema(description = "银行账户信息", example = "6222021234567890123")
    private String bankAccount;

    @Schema(description = "银行名称", example = "中国工商银行")
    private String bankName;

    @Schema(description = "创建时间", example = "2024-12-01 10:00:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2024-12-01 10:00:00")
    private LocalDateTime updateTime;
}



package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;

/**
 * 审批提交 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 审批提交 Request VO")
@Data
public class ApprovalSubmitReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "审批级别", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "审批级别不能为空")
    @Min(value = 1, message = "审批级别必须大于0")
    private Integer approvalLevel;

    @Schema(description = "审批类型", example = "order_approval")
    private String approvalType;

    @Schema(description = "审批备注", example = "请审批此培训订单")
    private String approvalRemark;
}




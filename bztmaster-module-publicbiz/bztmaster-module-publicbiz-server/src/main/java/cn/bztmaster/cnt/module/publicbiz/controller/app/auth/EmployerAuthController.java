package cn.bztmaster.cnt.module.publicbiz.controller.app.auth;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.tenant.core.context.TenantContextHolder;
import cn.bztmaster.cnt.module.publicbiz.controller.app.auth.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.auth.EmployerAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 雇主端小程序授权登录 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "雇主端 APP - 小程序授权登录")
@RestController
@RequestMapping("/publicbiz/employer/auth")
@Validated
@Slf4j
public class EmployerAuthController {

    @Resource
    private EmployerAuthService employerAuthService;

    @PostMapping("/weixin-mini-app-login")
    @Operation(summary = "微信小程序一键授权登录")
    @PermitAll
    public CommonResult<EmployerAuthLoginRespVO> weixinMiniAppLogin(
            @Valid @RequestBody EmployerAuthWeixinMiniAppLoginReqVO reqVO) {
        // 设置租户上下文，避免租户验证失败
        TenantContextHolder.setIgnore(true);
        TenantContextHolder.setTenantId(1L);

        try {
            return success(employerAuthService.weixinMiniAppLogin(reqVO));
        } catch (Exception e) {
            log.error("微信小程序一键授权登录失败", e);
            return CommonResult.error(5000, "登录失败: " + e.getMessage());
        }
    }

    @PostMapping("/sms-login")
    @Operation(summary = "手机验证码登录")
    @PermitAll
    public CommonResult<EmployerAuthLoginRespVO> smsLogin(@Valid @RequestBody EmployerAuthSmsLoginReqVO reqVO) {
        // 设置租户上下文，避免租户验证失败
        TenantContextHolder.setIgnore(true);
        TenantContextHolder.setTenantId(1L);

        try {
            return success(employerAuthService.smsLogin(reqVO));
        } catch (Exception e) {
            log.error("手机验证码登录失败", e);
            return CommonResult.error(5000, "登录失败: " + e.getMessage());
        }
    }

    @PostMapping("/send-sms-code")
    @Operation(summary = "发送手机验证码")
    @PermitAll
    public CommonResult<Boolean> sendSmsCode(@Valid @RequestBody EmployerAuthSmsSendReqVO reqVO) {
        // 设置租户上下文，避免租户验证失败
        TenantContextHolder.setIgnore(true);
        TenantContextHolder.setTenantId(1L);

        try {
            employerAuthService.sendSmsCode(reqVO);
            return success(true);
        } catch (Exception e) {
            log.error("发送手机验证码失败", e);
            return CommonResult.error(5000, "发送失败: " + e.getMessage());
        }
    }

    @PostMapping("/validate-sms-code")
    @Operation(summary = "校验手机验证码")
    @PermitAll
    public CommonResult<Boolean> validateSmsCode(@Valid @RequestBody EmployerAuthSmsValidateReqVO reqVO) {
        // 设置租户上下文，避免租户验证失败
        TenantContextHolder.setIgnore(true);
        TenantContextHolder.setTenantId(1L);

        try {
            employerAuthService.validateSmsCode(reqVO);
            return success(true);
        } catch (Exception e) {
            log.error("校验手机验证码失败", e);
            return CommonResult.error(5000, "校验失败: " + e.getMessage());
        }
    }

    @PostMapping("/refresh-token")
    @Operation(summary = "刷新令牌")
    @Parameter(name = "refreshToken", description = "刷新令牌", required = true)
    @PermitAll
    public CommonResult<EmployerAuthLoginRespVO> refreshToken(@RequestParam("refreshToken") String refreshToken) {
        // 设置租户上下文，避免租户验证失败
        TenantContextHolder.setIgnore(true);
        TenantContextHolder.setTenantId(1L);

        try {
            return success(employerAuthService.refreshToken(refreshToken));
        } catch (Exception e) {
            log.error("刷新令牌失败", e);
            return CommonResult.error(5000, "刷新失败: " + e.getMessage());
        }
    }

    @PutMapping("/user/update")
    @Operation(summary = "更新用户信息")
    public CommonResult<EmployerUserUpdateRespVO> updateUserInfo(@Valid @RequestBody EmployerUserUpdateReqVO reqVO) {
        // 设置租户上下文，避免租户验证失败
        TenantContextHolder.setIgnore(true);
        TenantContextHolder.setTenantId(1L);

        try {
            return success(employerAuthService.updateUserInfo(reqVO));
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return CommonResult.error(5000, "更新失败: " + e.getMessage());
        }
    }

}
package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class AgencyScheduleMonthRespVO {
    @Schema(description = "年份")
    private Integer year;
    @Schema(description = "月份")
    private Integer month;
    @Schema(description = "阿姨清单")
    private List<AuntSimpleVO> aunts;
    @Schema(description = "排班计数，键：阿姨ID -> 天 -> 数量")
    private Map<Long, Map<Integer, Integer>> schedules;

    @Data
    public static class AuntSimpleVO {
        private Long id;
        private String auntOneId;
        private String name;
    }
}



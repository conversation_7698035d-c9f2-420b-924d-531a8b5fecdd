package cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 雇主端小程序用户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MpUserMapper extends BaseMapperX<MpUserDO> {

    /**
     * 根据openid查询用户
     *
     * @param openid 微信openid
     * @return 用户信息
     */
    default MpUserDO selectByOpenid(String openid) {
        return selectOne(MpUserDO::getOpenid, openid);
    }

    /**
     * 根据手机号查询用户
     *
     * @param mobile 手机号
     * @return 用户信息
     */
    default MpUserDO selectByMobile(String mobile) {
        return selectOne(MpUserDO::getMobile, mobile);
    }

    /**
     * 根据appId和openid查询用户
     *
     * @param appId  小程序appId
     * @param openid 微信openid
     * @return 用户信息
     */
    default MpUserDO selectByAppIdAndOpenid(String appId, String openid) {
        return selectOne(new LambdaQueryWrapperX<MpUserDO>()
                .eq(MpUserDO::getAppId, appId)
                .eq(MpUserDO::getOpenid, openid));
    }

    /**
     * 根据昵称查询用户
     *
     * @param nickname 用户昵称
     * @return 用户信息
     */
    default MpUserDO selectByNickname(String nickname) {
        return selectOne(MpUserDO::getNickname, nickname);
    }

    /**
     * 根据OneID查询用户
     *
     * @param oneid 用户OneID
     * @return 用户信息
     */
    default MpUserDO selectByOneId(String oneid) {
        return selectOne(MpUserDO::getOneid, oneid);
    }

}
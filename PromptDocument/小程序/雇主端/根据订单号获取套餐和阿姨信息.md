# 根据订单号获取套餐和阿姨信息接口文档

## 接口基本信息

- **接口名称**: 根据订单号获取套餐和阿姨信息
- **接口路径**: `/publicbiz/employer/order/packages-and-aunt-info`
- **请求方法**: `GET`
- **接口描述**: 根据订单ID获取该订单关联的套餐信息和阿姨信息

## 请求参数

### 路径参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | Long | 是 | 订单ID |

### 请求示例
```
GET /publicbiz/employer/order/packages-and-aunt-info?orderId=12345
```

## 响应参数

### 响应结构
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "packageMainImage": "string",
    "packageName": "string", 
    "auntName": "string",
    "auntOneId": "string",
    "servicePackageId": "string",
    "agencyId": "string",
      "agencyName": "string"
  }
}
```

### 响应字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | Integer | 是 | 响应状态码，0表示成功 |
| message | String | 是 | 响应消息 |
| data | Object | 是 | 响应数据 |
| data.packageMainImage | String | 是 | 套餐主图URL |
| data.packageName | String | 是 | 套餐名称 |
| data.auntName | String | 否 | 阿姨名称|
| data.auntOneId | String | 否 | 阿姨oneID|
| data.servicePackageId | String | 是 | 服务套餐ID |
| data.agencyId | String | 是 | 机构ID |
| data.agencyName | String | 是 | 机构名称 |
## 响应示例

### 成功响应
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "packageMainImage": "https://qiniu.bzmaster.cn/20250730/package_main_image.jpg",
    "packageName": "临时保洁套餐A3",
    "auntName": "张阿姨",
    "auntOneId": "aunt_123456",
    "servicePackageId": "package_789",
    "agencyId": "agency_456",
    "agencyName": "测试机构A"
  }
}
```


### 失败响应
```json
{
  "code": 1001,
  "message": "订单不存在",
  "data": null
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1001 | 订单不存在 |
| 1002 | 订单ID不能为空 |
| 5000 | 系统内部错误 |


阿姨订单任务表表结构：
CREATE TABLE `publicbiz_domestic_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `domestic_order_id` bigint(20) NOT NULL COMMENT '家政服务订单ID',
  `task_no` varchar(50) NOT NULL COMMENT '任务编号',
  `task_sequence` int(11) NOT NULL COMMENT '任务序号',
  `task_name` varchar(200) NOT NULL COMMENT '任务名称',
  `task_description` text COMMENT '任务描述',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型',
  `task_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '任务状态：pending-待分配/assigned-已分配/in_progress-进行中/completed-已完成/cancelled-已取消',
  `planned_start_time` datetime DEFAULT NULL COMMENT '计划开始时间',
  `planned_end_time` datetime DEFAULT NULL COMMENT '计划结束时间',
  `actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_time` datetime DEFAULT NULL COMMENT '实际结束时间',
  `duration` varchar(50) DEFAULT NULL COMMENT '任务时长',
  `practitioner_oneid` varchar(36) DEFAULT NULL COMMENT '服务人员OneID',
  `practitioner_name` varchar(50) DEFAULT NULL COMMENT '服务人员姓名',
  `practitioner_phone` varchar(20) DEFAULT NULL COMMENT '服务人员电话',
  `schedule_date` date NOT NULL COMMENT '排班日期',
  `service_category_id` bigint(20) NOT NULL COMMENT '服务分类ID',
  `service_category_name` varchar(255) NOT NULL COMMENT '服务分类名称',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `customer_name` varchar(64) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `service_address` varchar(255) NOT NULL COMMENT '服务地址',
  `punch_in_time` datetime DEFAULT NULL COMMENT '打卡开始时间',
  `punch_out_time` datetime DEFAULT NULL COMMENT '打卡结束时间',
  `punch_location` varchar(255) DEFAULT NULL COMMENT '打卡位置',
  `punch_latitude` decimal(10,7) DEFAULT NULL COMMENT '打卡纬度',
  `punch_longitude` decimal(10,7) DEFAULT NULL COMMENT '打卡经度',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_no` (`task_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_domestic_order_id` (`domestic_order_id`),
  KEY `idx_task_sequence` (`task_sequence`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_practitioner_oneId` (`practitioner_oneid`),
  KEY `idx_planned_start_time` (`planned_start_time`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=76 DEFAULT CHARSET=utf8mb4 COMMENT='家政服务任务表';
package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 统计响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 统计响应 VO")
@Data
public class StatisticsRespVO {

    @Schema(description = "总订单数", example = "100")
    private Integer totalOrders;

    @Schema(description = "待处理订单数", example = "25")
    private Integer pendingOrders;

    @Schema(description = "月度金额", example = "50000.00")
    private BigDecimal monthlyAmount;

    @Schema(description = "完成率", example = "75.00")
    private BigDecimal completionRate;
}




package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 审批提交 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 审批提交 Response VO")
@Data
public class ApprovalSubmitRespVO {

    @Schema(description = "审批ID", example = "1")
    private Long approvalId;

    @Schema(description = "审批单号", example = "APV123456789")
    private String approvalNo;
}




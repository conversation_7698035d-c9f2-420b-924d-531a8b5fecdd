package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
 * 审批通过 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 审批通过 Request VO")
@Data
public class ApprovalApproveReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "审批结果", requiredMode = Schema.RequiredMode.REQUIRED, example = "approved")
    @NotBlank(message = "审批结果不能为空")
    private String approvalResult;

    @Schema(description = "审批意见", example = "同意此培训订单")
    private String approvalOpinion;

    @Schema(description = "审批人姓名", example = "李经理")
    private String approverName;
}




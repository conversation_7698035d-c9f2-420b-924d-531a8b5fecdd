package cn.bztmaster.cnt.module.publicbiz.service.partner;

import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.framework.test.core.ut.BaseDbUnitTest;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo.PartnerSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo.PartnerUpdateReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.partner.PartnerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.partner.PartnerMapper;
import cn.bztmaster.cnt.module.publicbiz.service.partner.impl.PartnerServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.Date;

import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.PARTNER_NAME_DUPLICATE;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link PartnerServiceImpl} 合作伙伴名称唯一性验证的单元测试
 */
@Import(PartnerServiceImpl.class)
public class PartnerServiceImplNameValidationTest extends BaseDbUnitTest {

    @Resource
    private PartnerServiceImpl partnerService;

    @Resource
    private PartnerMapper partnerMapper;

    @Test
    public void testCreatePartner_WithDuplicateName_ShouldThrowException() {
        // 准备数据：先创建一个合作伙伴
        PartnerDO existingPartner = new PartnerDO();
        existingPartner.setName("测试合作伙伴");
        existingPartner.setShortName("测试");
        existingPartner.setType("企业");
        existingPartner.setDeleted(false);
        existingPartner.setCreateTime(new Date());
        existingPartner.setTenantId(1L);
        partnerMapper.insert(existingPartner);

        // 尝试创建同名的合作伙伴
        PartnerSaveReqVO reqVO = new PartnerSaveReqVO();
        reqVO.setName("测试合作伙伴");
        reqVO.setShortName("测试2");
        reqVO.setType("机构");

        // 验证抛出异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            partnerService.createPartner(reqVO);
        });

        assertEquals(PARTNER_NAME_DUPLICATE.getCode(), exception.getCode());
        assertEquals(PARTNER_NAME_DUPLICATE.getMsg(), exception.getMessage());
    }

    @Test
    public void testCreatePartner_WithUniqueName_ShouldSuccess() {
        // 创建唯一名称的合作伙伴
        PartnerSaveReqVO reqVO = new PartnerSaveReqVO();
        reqVO.setName("唯一合作伙伴");
        reqVO.setShortName("唯一");
        reqVO.setType("企业");

        // 验证创建成功
        assertDoesNotThrow(() -> {
            Long partnerId = partnerService.createPartner(reqVO);
            assertNotNull(partnerId);
        });
    }

    @Test
    public void testUpdatePartner_WithDuplicateName_ShouldThrowException() {
        // 准备数据：创建两个合作伙伴
        PartnerDO partner1 = new PartnerDO();
        partner1.setName("合作伙伴1");
        partner1.setShortName("伙伴1");
        partner1.setType("企业");
        partner1.setDeleted(false);
        partner1.setCreateTime(new Date());
        partner1.setTenantId(1L);
        partnerMapper.insert(partner1);

        PartnerDO partner2 = new PartnerDO();
        partner2.setName("合作伙伴2");
        partner2.setShortName("伙伴2");
        partner2.setType("机构");
        partner2.setDeleted(false);
        partner2.setCreateTime(new Date());
        partner2.setTenantId(1L);
        partnerMapper.insert(partner2);

        // 尝试将partner2的名称改为与partner1相同
        PartnerUpdateReqVO reqVO = new PartnerUpdateReqVO();
        reqVO.setId(partner2.getId());
        reqVO.setName("合作伙伴1"); // 与partner1重名
        reqVO.setShortName("伙伴2");
        reqVO.setType("机构");

        // 验证抛出异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            partnerService.updatePartner(reqVO);
        });

        assertEquals(PARTNER_NAME_DUPLICATE.getCode(), exception.getCode());
    }

    @Test
    public void testUpdatePartner_WithSameName_ShouldSuccess() {
        // 准备数据：创建一个合作伙伴
        PartnerDO partner = new PartnerDO();
        partner.setName("原始合作伙伴");
        partner.setShortName("原始");
        partner.setType("企业");
        partner.setDeleted(false);
        partner.setCreateTime(new Date());
        partner.setTenantId(1L);
        partnerMapper.insert(partner);

        // 更新合作伙伴但保持名称不变
        PartnerUpdateReqVO reqVO = new PartnerUpdateReqVO();
        reqVO.setId(partner.getId());
        reqVO.setName("原始合作伙伴"); // 保持原名称
        reqVO.setShortName("更新后");
        reqVO.setType("机构");

        // 验证更新成功
        assertDoesNotThrow(() -> {
            partnerService.updatePartner(reqVO);
        });
    }

    @Test
    public void testValidatePartnerNameUnique_WithBlankName_ShouldNotThrowException() {
        // 验证空白名称不会抛出异常
        assertDoesNotThrow(() -> {
            partnerService.validatePartnerNameUnique(null, "");
            partnerService.validatePartnerNameUnique(null, "   ");
            partnerService.validatePartnerNameUnique(null, null);
        });
    }

    @Test
    public void testValidatePartnerNameUnique_WithDeletedPartner_ShouldNotThrowException() {
        // 准备数据：创建一个已删除的合作伙伴
        PartnerDO deletedPartner = new PartnerDO();
        deletedPartner.setName("已删除合作伙伴");
        deletedPartner.setShortName("已删除");
        deletedPartner.setType("企业");
        deletedPartner.setDeleted(true); // 标记为已删除
        deletedPartner.setCreateTime(new Date());
        deletedPartner.setTenantId(1L);
        partnerMapper.insert(deletedPartner);

        // 验证可以创建同名的合作伙伴（因为原来的已删除）
        assertDoesNotThrow(() -> {
            partnerService.validatePartnerNameUnique(null, "已删除合作伙伴");
        });
    }
}

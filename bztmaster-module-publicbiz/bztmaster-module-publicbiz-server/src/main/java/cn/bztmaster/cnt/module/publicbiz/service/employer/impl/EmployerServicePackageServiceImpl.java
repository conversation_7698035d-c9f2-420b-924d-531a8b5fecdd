package cn.bztmaster.cnt.module.publicbiz.service.employer.impl;

import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackagePageRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackageQueryReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackageRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackageDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackageReviewRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.AgencyDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackageRecommendRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.employer.EmployerServicePackageService;
import cn.bztmaster.cnt.module.publicbiz.convert.employer.ServicePackageConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.AuntReviewDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageFeatureDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.AuntReviewMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.DomesticOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.ServicePackageFeatureMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.ServicePackageMapper;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 雇主端服务套餐 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class EmployerServicePackageServiceImpl implements EmployerServicePackageService {

    @Resource
    private ServicePackageMapper servicePackageMapper;
    @Resource
    private AgencyMapper agencyMapper;
    @Resource
    private ServicePackageFeatureMapper servicePackageFeatureMapper;
    @Resource
    private AuntReviewMapper auntReviewMapper;
    @Resource
    private DomesticOrderMapper domesticOrderMapper;

    // JSON解析器
    private static final ObjectMapper objectMapper = new ObjectMapper();

    // 默认位置（成都）
    private static final BigDecimal DEFAULT_LONGITUDE = new BigDecimal("104.0665");
    private static final BigDecimal DEFAULT_LATITUDE = new BigDecimal("30.5728");
    private static final BigDecimal DEFAULT_SATISFACTION_RATE = BigDecimal.ZERO;

    @Override
    @Cacheable(value = "servicePackage", key = "#reqVO.toString()")
    public PageResult<ServicePackageRespVO> getServicePackagePage(ServicePackageQueryReqVO reqVO) {
        // 0. 参数验证和默认值设置
        validateAndSetDefaults(reqVO);

        // 1. 查询套餐列表
        List<ServicePackageDO> packageList = servicePackageMapper.selectPackageListByCondition(reqVO);
        if (packageList.isEmpty()) {
            return new PageResult<>(new ArrayList<>(), 0L);
        }

        // 2. 获取关联数据
        Map<Long, AgencyDO> agencyMap = getAgencyMap(packageList);
        Map<Long, List<String>> featureMap = getFeatureMap(packageList);
        Map<Long, BigDecimal> satisfactionMap = getSatisfactionMap(packageList);

        // 3. 计算距离
        Map<Long, Double> distanceMap = calculateDistances(packageList, agencyMap, reqVO);

        // 4. 转换为响应对象
        List<ServicePackageRespVO> respList = convertToRespList(packageList, agencyMap, featureMap, satisfactionMap,
                distanceMap);

        // 5. 返回分页结果
        return new PageResult<>(respList, (long) packageList.size());
    }

    /**
     * 参数验证和默认值设置
     */
    private void validateAndSetDefaults(ServicePackageQueryReqVO reqVO) {
        // 验证经纬度范围
        if (reqVO.getLongitude().compareTo(new BigDecimal("-180")) < 0 ||
                reqVO.getLongitude().compareTo(new BigDecimal("180")) > 0) {
            log.warn("经度超出范围，使用默认位置");
            reqVO.setLongitude(DEFAULT_LONGITUDE);
        }
        if (reqVO.getLatitude().compareTo(new BigDecimal("-90")) < 0 ||
                reqVO.getLatitude().compareTo(new BigDecimal("90")) > 0) {
            log.warn("纬度超出范围，使用默认位置");
            reqVO.setLatitude(DEFAULT_LATITUDE);
        }

        // 设置默认值
        if (reqVO.getPage() == null || reqVO.getPage() < 1) {
            reqVO.setPage(1);
        }
        if (reqVO.getPageSize() == null || reqVO.getPageSize() < 1) {
            reqVO.setPageSize(20);
        }
        if (reqVO.getSortType() == null) {
            reqVO.setSortType("distance");
        }

        // 计算偏移量
        reqVO.setOffset((reqVO.getPage() - 1) * reqVO.getPageSize());
    }

    /**
     * 查询套餐列表
     */
    private List<ServicePackageDO> queryPackageList(ServicePackageQueryReqVO reqVO) {
        int offset = (reqVO.getPage() - 1) * reqVO.getPageSize();
        int limit = reqVO.getPageSize() * 10; // 查询更多数据用于排序

        return servicePackageMapper.selectPackageListByCondition(reqVO);
    }

    /**
     * 获取机构信息映射
     */

    private Map<Long, AgencyDO> getAgencyMap(List<ServicePackageDO> packageList) {
        List<Long> agencyIds = packageList.stream()
                .map(ServicePackageDO::getAgencyId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (agencyIds == null || agencyIds.isEmpty()) {
            return new HashMap<>();
        }

        List<AgencyDO> agencyList = agencyMapper.selectAgencyListByIds(agencyIds);
        return agencyList.stream()
                .collect(Collectors.toMap(AgencyDO::getId, agency -> agency));
    }

    /**
     * 获取特色标签映射
     */

    private Map<Long, List<String>> getFeatureMap(List<ServicePackageDO> packageList) {
        List<Long> packageIds = packageList.stream()
                .map(ServicePackageDO::getId)
                .collect(Collectors.toList());

        if (packageIds == null || packageIds.isEmpty()) {
            return new HashMap<>();
        }

        List<ServicePackageFeatureDO> featureList = servicePackageFeatureMapper
                .selectFeatureListByPackageIds(packageIds);
        return featureList.stream()
                .collect(Collectors.groupingBy(
                        ServicePackageFeatureDO::getPackageId,
                        Collectors.mapping(ServicePackageFeatureDO::getFeatureName, Collectors.toList())));
    }

    /**
     * 获取满意度评分映射
     */
    private Map<Long, BigDecimal> getSatisfactionMap(List<ServicePackageDO> packageList) {
        Map<Long, BigDecimal> satisfactionMap = new HashMap<>();

        for (ServicePackageDO pkg : packageList) {
            BigDecimal rating = auntReviewMapper.selectAverageRatingByPackageId(pkg.getId());
            satisfactionMap.put(pkg.getId(), rating != null ? rating : DEFAULT_SATISFACTION_RATE);
        }

        return satisfactionMap;
    }

    /**
     * 计算距离映射
     */
    private Map<Long, Double> calculateDistances(List<ServicePackageDO> packageList,
            Map<Long, AgencyDO> agencyMap,
            ServicePackageQueryReqVO reqVO) {
        Map<Long, Double> distanceMap = new HashMap<>();

        for (ServicePackageDO pkg : packageList) {
            AgencyDO agency = agencyMap.get(pkg.getAgencyId());
            if (agency != null && agency.getLongitude() != null && agency.getLatitude() != null) {
                double distance = calculateDistance(
                        reqVO.getLatitude().doubleValue(),
                        reqVO.getLongitude().doubleValue(),
                        agency.getLatitude().doubleValue(),
                        agency.getLongitude().doubleValue());
                distanceMap.put(pkg.getId(), distance);
            } else {
                distanceMap.put(pkg.getId(), Double.MAX_VALUE);
            }
        }

        return distanceMap;
    }

    /**
     * 使用Haversine公式计算两点间距离
     */
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final int R = 6371; // 地球半径（公里）

        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                        * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }

    /**
     * 根据排序类型排序套餐列表
     */
    private void sortPackageList(List<ServicePackageDO> packageList, String sortType,
            Map<Long, BigDecimal> satisfactionMap,
            Map<Long, Double> distanceMap,
            ServicePackageQueryReqVO reqVO) {
        switch (sortType) {
            case "distance":
                packageList.sort((p1, p2) -> {
                    Double d1 = distanceMap.getOrDefault(p1.getId(), Double.MAX_VALUE);
                    Double d2 = distanceMap.getOrDefault(p2.getId(), Double.MAX_VALUE);
                    return d1.compareTo(d2);
                });
                break;
            case "price":
                packageList.sort((p1, p2) -> {
                    int priceCompare = p1.getPrice().compareTo(p2.getPrice());
                    if (priceCompare == 0) {
                        return p2.getCreateTime().compareTo(p1.getCreateTime());
                    }
                    return priceCompare;
                });
                break;
            case "satisfaction":
                packageList.sort((p1, p2) -> {
                    BigDecimal s1 = satisfactionMap.getOrDefault(p1.getId(), DEFAULT_SATISFACTION_RATE);
                    BigDecimal s2 = satisfactionMap.getOrDefault(p2.getId(), DEFAULT_SATISFACTION_RATE);
                    int satisfactionCompare = s2.compareTo(s1);
                    if (satisfactionCompare == 0) {
                        Double d1 = distanceMap.getOrDefault(p1.getId(), Double.MAX_VALUE);
                        Double d2 = distanceMap.getOrDefault(p2.getId(), Double.MAX_VALUE);
                        return d1.compareTo(d2);
                    }
                    return satisfactionCompare;
                });
                break;
            case "comprehensive":
                sortByComprehensive(packageList, satisfactionMap, distanceMap, reqVO);
                break;
            default:
                // 默认按距离排序
                packageList.sort((p1, p2) -> {
                    Double d1 = distanceMap.getOrDefault(p1.getId(), Double.MAX_VALUE);
                    Double d2 = distanceMap.getOrDefault(p2.getId(), Double.MAX_VALUE);
                    return d1.compareTo(d2);
                });
        }
    }

    /**
     * 综合排序
     */
    private void sortByComprehensive(List<ServicePackageDO> packageList,
            Map<Long, BigDecimal> satisfactionMap,
            Map<Long, Double> distanceMap,
            ServicePackageQueryReqVO reqVO) {
        // 获取同类套餐最高价格
        BigDecimal maxPrice = getMaxPriceByCategory(packageList);
        // 获取最大距离
        double maxDistance = getMaxDistance(distanceMap);
        // 获取机构评分映射
        Map<Long, BigDecimal> agencyRatingMap = getAgencyRatingMap(packageList);

        packageList.sort((p1, p2) -> {
            double score1 = calculateComprehensiveScore(p1,
                    satisfactionMap.getOrDefault(p1.getId(), DEFAULT_SATISFACTION_RATE),
                    distanceMap.getOrDefault(p1.getId(), Double.MAX_VALUE), maxPrice, maxDistance,
                    agencyRatingMap.getOrDefault(p1.getAgencyId(), DEFAULT_SATISFACTION_RATE));
            double score2 = calculateComprehensiveScore(p2,
                    satisfactionMap.getOrDefault(p2.getId(), DEFAULT_SATISFACTION_RATE),
                    distanceMap.getOrDefault(p2.getId(), Double.MAX_VALUE), maxPrice, maxDistance,
                    agencyRatingMap.getOrDefault(p2.getAgencyId(), DEFAULT_SATISFACTION_RATE));
            return Double.compare(score2, score1); // 降序排列
        });
    }

    /**
     * 计算综合评分
     */
    private double calculateComprehensiveScore(ServicePackageDO pkg, BigDecimal satisfactionRate,
            Double distance, BigDecimal maxPrice, double maxDistance,
            BigDecimal agencyRating) {
        // 机构评分权重分数 = (机构所有套餐的综合评分 / 5.0) × 100
        double agencyScore = agencyRating.divide(new BigDecimal("5.0"), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100")).doubleValue();

        // 距离权重分数 = (1 - 实际距离/最大距离) × 100
        double distanceScore = (1 - distance / maxDistance) * 100;

        // 满意度权重分数 = (该套餐满意度评分 / 5.0) × 100
        double satisfactionScore = satisfactionRate.divide(new BigDecimal("5.0"), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100")).doubleValue();

        // 价格权重分数 = (1 - 套餐价格/同类套餐最高价格) × 100
        double priceScore = (1 - pkg.getPrice().divide(maxPrice, 4, RoundingMode.HALF_UP).doubleValue()) * 100;

        // 综合评分 = 机构评分权重×40% + 距离权重×25% + 满意度权重×25% + 价格权重×10%
        return agencyScore * 0.4 + distanceScore * 0.25 + satisfactionScore * 0.25 + priceScore * 0.1;
    }

    /**
     * 获取同类套餐最高价格
     */
    private BigDecimal getMaxPriceByCategory(List<ServicePackageDO> packageList) {
        if (packageList == null || packageList.isEmpty()) {
            return BigDecimal.ONE;
        }

        // 获取第一个套餐的分类ID
        Long categoryId = packageList.get(0).getCategoryId();
        if (categoryId == null) {
            return packageList.stream()
                    .map(ServicePackageDO::getPrice)
                    .max(BigDecimal::compareTo)
                    .orElse(BigDecimal.ONE);
        }

        BigDecimal maxPrice = servicePackageMapper.selectMaxPriceByCategory(categoryId);
        return maxPrice != null ? maxPrice : BigDecimal.ONE;
    }

    /**
     * 获取最大距离
     */
    private double getMaxDistance(Map<Long, Double> distanceMap) {
        return distanceMap.values().stream()
                .mapToDouble(Double::doubleValue)
                .max()
                .orElse(1.0);
    }

    /**
     * 获取机构评分映射
     */
    private Map<Long, BigDecimal> getAgencyRatingMap(List<ServicePackageDO> packageList) {
        Map<Long, BigDecimal> agencyRatingMap = new HashMap<>();

        Set<Long> agencyIds = packageList.stream()
                .map(ServicePackageDO::getAgencyId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        for (Long agencyId : agencyIds) {
            BigDecimal rating = auntReviewMapper.selectAverageRatingByAgencyId(agencyId);
            agencyRatingMap.put(agencyId, rating != null ? rating : DEFAULT_SATISFACTION_RATE);
        }

        return agencyRatingMap;
    }

    /**
     * 应用分页
     */
    private List<ServicePackageDO> applyPagination(List<ServicePackageDO> packageList, ServicePackageQueryReqVO reqVO) {
        int startIndex = (reqVO.getPage() - 1) * reqVO.getPageSize();
        int endIndex = Math.min(startIndex + reqVO.getPageSize(), packageList.size());

        if (startIndex >= packageList.size()) {
            return new ArrayList<>();
        }

        return packageList.subList(startIndex, endIndex);
    }

    /**
     * 转换为响应列表
     */
    private List<ServicePackageRespVO> convertToRespList(List<ServicePackageDO> packageList,
            Map<Long, AgencyDO> agencyMap,
            Map<Long, List<String>> featureMap,
            Map<Long, BigDecimal> satisfactionMap,
            Map<Long, Double> distanceMap) {
        List<ServicePackageRespVO> respList = new ArrayList<>();

        for (ServicePackageDO pkg : packageList) {
            AgencyDO agency = agencyMap.get(pkg.getAgencyId());
            List<String> features = featureMap.getOrDefault(pkg.getId(), new ArrayList<>());
            BigDecimal satisfactionRate = satisfactionMap.getOrDefault(pkg.getId(), DEFAULT_SATISFACTION_RATE);
            Double distance = distanceMap.getOrDefault(pkg.getId(), Double.MAX_VALUE);

            ServicePackageRespVO respVO = ServicePackageConvert.INSTANCE.convert(
                    pkg, agency, features, distance, satisfactionRate);
            // 规范满意度评分：限制在0-5区间，且只保留一位小数
            respVO.setSatisfactionRate(normalizeSatisfactionRate(respVO.getSatisfactionRate()));
            respList.add(respVO);
        }

        return respList;
    }

    /**
     * 规范满意度评分：限制在0-5之间，保留一位小数（四舍五入）
     */
    private BigDecimal normalizeSatisfactionRate(BigDecimal rate) {
        if (rate == null) {
            return BigDecimal.ZERO.setScale(1, RoundingMode.HALF_UP);
        }
        BigDecimal clamped = rate;
        if (clamped.compareTo(BigDecimal.ZERO) < 0) {
            clamped = BigDecimal.ZERO;
        } else if (clamped.compareTo(new BigDecimal("5")) > 0) {
            clamped = new BigDecimal("5");
        }
        return clamped.setScale(1, RoundingMode.HALF_UP);
    }

    @Override
    public ServicePackageDetailRespVO getServicePackageDetail(Long id) {
        // 1. 查询套餐基本信息
        ServicePackageDO servicePackage = servicePackageMapper.selectById(id);
        if (servicePackage == null || servicePackage.getDeleted()) {
            throw new RuntimeException("套餐不存在或已删除");
        }

        // 2. 验证套餐状态
        if (!"active".equals(servicePackage.getStatus()) || !"approved".equals(servicePackage.getAuditStatus())) {
            throw new RuntimeException("套餐未上架或未通过审核");
        }

        // 3. 查询套餐轮播图
        List<String> carouselImages = new ArrayList<>(); // 暂时返回空列表，实际项目中需要查询轮播图表

        // 4. 转换为响应对象
        ServicePackageDetailRespVO detailRespVO = convertToDetailRespVO(servicePackage, carouselImages);

        return detailRespVO;
    }

    @Override
    public ServicePackageReviewRespVO getServicePackageReviews(Long packageId, Integer page, Integer size) {
        // 1. 参数验证
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1 || size > 50) {
            size = 10;
        }

        // 2. 查询评价总数
        Long total = auntReviewMapper.selectCountByServicePackageId(packageId);

        // 3. 计算分页参数
        Long offset = (long) ((page - 1) * size);
        Long limit = (long) size;

        // 4. 查询评价列表
        List<AuntReviewDO> reviewList = auntReviewMapper.selectPageByServicePackageId(packageId, offset, limit);

        // 5. 转换为响应对象
        List<ServicePackageReviewRespVO.ReviewRecord> records = convertToReviewRecords(reviewList);

        // 6. 构建分页结果
        ServicePackageReviewRespVO reviewRespVO = new ServicePackageReviewRespVO();
        reviewRespVO.setTotal(total);
        reviewRespVO.setPages((total + size - 1) / size);
        reviewRespVO.setCurrent((long) page);
        reviewRespVO.setSize((long) size);
        reviewRespVO.setRecords(records);

        return reviewRespVO;
    }

    @Override
    public AgencyDetailRespVO getAgencyDetail(Long packageId) {
        // 1. 查询套餐信息获取机构ID
        ServicePackageDO servicePackage = servicePackageMapper.selectById(packageId);
        if (servicePackage == null || servicePackage.getDeleted()) {
            throw new RuntimeException("套餐不存在或已删除");
        }

        Long agencyId = servicePackage.getAgencyId();
        if (agencyId == null) {
            throw new RuntimeException("套餐未关联机构");
        }

        // 2. 查询机构基本信息
        AgencyDO agency = agencyMapper.selectById(agencyId);
        if (agency == null || agency.getDeleted()) {
            throw new RuntimeException("机构不存在或已删除");
        }

        // 3. 查询机构资质文件
        List<AgencyDetailRespVO.Certificate> certificates = getAgencyCertificates(agencyId);

        // 4. 转换为响应对象
        AgencyDetailRespVO agencyDetailRespVO = convertToAgencyDetailRespVO(agency, certificates);

        return agencyDetailRespVO;
    }

    @Override
    public ServicePackageRecommendRespVO getServicePackageRecommendations(Long packageId, Integer page, Integer size) {
        // 1. 参数验证
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1 || size > 50) {
            size = 10;
        }

        // 2. 查询当前套餐信息
        ServicePackageDO currentPackage = servicePackageMapper.selectById(packageId);
        if (currentPackage == null || currentPackage.getDeleted()) {
            throw new RuntimeException("套餐不存在或已删除");
        }

        // 3. 查询同机构的其他套餐作为推荐
        List<ServicePackageDO> recommendList = new ArrayList<>(); // 暂时返回空列表，实际项目中需要查询数据库

        // 4. 查询总数
        Long total = 0L; // 暂时返回0，实际项目中需要查询数据库

        // 5. 转换为响应对象
        List<ServicePackageRecommendRespVO.RecommendRecord> records = convertToRecommendRecords(recommendList);

        // 6. 构建分页结果
        ServicePackageRecommendRespVO recommendRespVO = new ServicePackageRecommendRespVO();
        recommendRespVO.setTotal(total);
        recommendRespVO.setPages((total + size - 1) / size);
        recommendRespVO.setCurrent((long) page);
        recommendRespVO.setSize((long) size);
        recommendRespVO.setRecords(records);

        return recommendRespVO;
    }

    /**
     * 转换为套餐详情响应对象
     */
    private ServicePackageDetailRespVO convertToDetailRespVO(ServicePackageDO servicePackage,
            List<String> carouselImages) {
        ServicePackageDetailRespVO detailRespVO = new ServicePackageDetailRespVO();

        // 基本信息
        detailRespVO.setId(servicePackage.getId());
        detailRespVO.setName(servicePackage.getName());
        detailRespVO.setCategory(servicePackage.getCategory());
        detailRespVO.setThumbnail(servicePackage.getThumbnail());
        detailRespVO.setPrice(servicePackage.getPrice());
        detailRespVO.setOriginalPrice(servicePackage.getOriginalPrice());
        detailRespVO.setUnit(servicePackage.getUnit());
        detailRespVO.setServiceDuration(servicePackage.getServiceDuration());
        detailRespVO.setPackageType(servicePackage.getPackageType());
        detailRespVO.setServiceDescription(servicePackage.getServiceDescription());
        detailRespVO.setServiceDetails(servicePackage.getServiceDetails());
        detailRespVO.setServiceProcess(servicePackage.getServiceProcess());
        detailRespVO.setPurchaseNotice(servicePackage.getPurchaseNotice());
        detailRespVO.setStatus(servicePackage.getStatus());
        detailRespVO.setAdvanceBookingDays(servicePackage.getAdvanceBookingDays());
        detailRespVO.setTimeSelectionMode(servicePackage.getTimeSelectionMode());
        detailRespVO.setAppointmentMode(servicePackage.getAppointmentMode());
        detailRespVO.setServiceStartTime(servicePackage.getServiceStartTime());
        detailRespVO.setAddressSetting(servicePackage.getAddressSetting());
        detailRespVO.setMaxBookingDays(servicePackage.getMaxBookingDays());
        detailRespVO.setCancellationPolicy(servicePackage.getCancellationPolicy());
        detailRespVO.setAuditStatus(servicePackage.getAuditStatus());
        detailRespVO.setAgencyId(servicePackage.getAgencyId());
        detailRespVO.setAgencyName(servicePackage.getAgencyName());
        detailRespVO.setCategoryId(servicePackage.getCategoryId());
        detailRespVO.setServiceTimeStart(
                servicePackage.getServiceTimeStart() != null ? servicePackage.getServiceTimeStart().toString() : null);
        detailRespVO.setServiceTimeEnd(
                servicePackage.getServiceTimeEnd() != null ? servicePackage.getServiceTimeEnd().toString() : null);
        detailRespVO.setRestDayType(servicePackage.getRestDayType());
        detailRespVO.setServiceTimespan(servicePackage.getServiceTimespan());
        detailRespVO.setServiceTimes(servicePackage.getServiceTimes());
        detailRespVO.setValidityPeriod(servicePackage.getValidityPeriod());
        detailRespVO.setValidityPeriodUnit(servicePackage.getValidityPeriodUnit());
        detailRespVO.setServiceIntervalType(servicePackage.getServiceIntervalType());
        detailRespVO.setServiceIntervalValue(servicePackage.getServiceIntervalValue());
        detailRespVO.setSingleDurationHours(servicePackage.getSingleDurationHours());
        detailRespVO.setCreateTime(servicePackage.getCreateTime());
        detailRespVO.setUpdateTime(servicePackage.getUpdateTime());

        // 前端展示字段
        detailRespVO.setTitle(servicePackage.getName());
        detailRespVO.setDuration(servicePackage.getServiceDuration() + " | 每日" +
                (servicePackage.getSingleDurationHours() != null ? servicePackage.getSingleDurationHours() : 0) + "小时");
        detailRespVO.setImage(servicePackage.getThumbnail());
        detailRespVO.setTag("长周期套餐".equals(servicePackage.getPackageType()) ? "长周期套餐" : "次数次卡套餐");
        detailRespVO.setCurrentPrice(servicePackage.getPrice() != null ? servicePackage.getPrice().toString() : "0.00");
        detailRespVO.setOriginalPriceDisplay(
                servicePackage.getOriginalPrice() != null ? servicePackage.getOriginalPrice().toString() : "0.00");
        // 根据套餐ID统计订单数量
        Integer serviceCount = getServiceCountByPackageId(servicePackage.getId());
        detailRespVO.setServiceCount("已服务 " + serviceCount + "次");
        // 构建规格信息：根据套餐分类和类型
        String specification = buildSpecification(servicePackage);
        detailRespVO.setSpecification(specification);
        detailRespVO.setImages(carouselImages);

        return detailRespVO;
    }

    /**
     * 转换为评价记录列表
     */
    private List<ServicePackageReviewRespVO.ReviewRecord> convertToReviewRecords(List<AuntReviewDO> reviewList) {
        List<ServicePackageReviewRespVO.ReviewRecord> records = new ArrayList<>();

        for (AuntReviewDO review : reviewList) {
            ServicePackageReviewRespVO.ReviewRecord record = new ServicePackageReviewRespVO.ReviewRecord();

            record.setId(review.getId());
            record.setOrderId(review.getOrderId());
            record.setAuntId(review.getAuntId() != null ? review.getAuntId().toString() : null);
            record.setReviewerId(review.getReviewerId());
            record.setReviewerName(review.getReviewerName());
            record.setReviewerAvatar(review.getReviewerAvatar());
            record.setRating(review.getRating());
            record.setReviewTags(parseReviewTags(review.getReviewTags()));
            record.setReviewContent(review.getReviewContent());
            record.setReviewImages(parseReviewImages(review.getReviewImages()));
            record.setReviewType(review.getReviewType());
            record.setIsAnonymous(review.getIsAnonymous() != null ? (review.getIsAnonymous() ? 1 : 0) : 0);
            record.setIsRecommend(review.getIsRecommend() != null ? (review.getIsRecommend() ? 1 : 0) : 0);
            record.setLikeCount(review.getLikeCount());
            record.setReplyContent(review.getReplyContent());
            record.setReplyTime(review.getReplyTime() != null ? LocalDateTime.parse(review.getReplyTime()) : null);
            record.setStatus(review.getStatus() != null ? (review.getStatus() ? 1 : 0) : 1);
            record.setAgencyId(review.getAgencyId());
            record.setServicePackageId(review.getServicePackageId());
            record.setCreateTime(review.getCreateTime());

            // 前端展示字段
            record.setName(review.getReviewerName());
            record.setAvatar(review.getReviewerAvatar());
            record.setDate(review.getCreateTime() != null ? review.getCreateTime().toLocalDate().toString() : "");
            record.setContent(review.getReviewContent());

            records.add(record);
        }

        return records;
    }

    /**
     * 获取机构资质文件
     */
    private List<AgencyDetailRespVO.Certificate> getAgencyCertificates(Long agencyId) {
        // 这里需要根据实际的资质文件表结构来实现
        // 暂时返回空列表，实际项目中需要查询对应的资质文件表
        return new ArrayList<>();
    }

    /**
     * 转换为机构详情响应对象
     */
    private AgencyDetailRespVO convertToAgencyDetailRespVO(AgencyDO agency,
            List<AgencyDetailRespVO.Certificate> certificates) {
        AgencyDetailRespVO agencyDetailRespVO = new AgencyDetailRespVO();

        // 基本信息
        agencyDetailRespVO.setId(agency.getId());
        agencyDetailRespVO.setAgencyNo(agency.getAgencyNo());
        agencyDetailRespVO.setName(agency.getAgencyName());
        agencyDetailRespVO.setShortName(agency.getAgencyShortName());
        agencyDetailRespVO.setAgencyType(agency.getAgencyType());
        agencyDetailRespVO.setLegalRepresentative(agency.getLegalRepresentative());
        agencyDetailRespVO.setUnifiedSocialCreditCode(agency.getUnifiedSocialCreditCode());
        agencyDetailRespVO.setEstablishmentDate(agency.getEstablishmentDate());
        agencyDetailRespVO.setRegisteredAddress(agency.getRegisteredAddress());
        agencyDetailRespVO.setOperatingAddress(agency.getOperatingAddress());
        agencyDetailRespVO.setBusinessScope(agency.getBusinessScope());
        agencyDetailRespVO.setContactPerson(agency.getContactPerson());
        agencyDetailRespVO.setContactPhone(agency.getContactPhone());
        agencyDetailRespVO.setContactEmail(agency.getContactEmail());
        agencyDetailRespVO.setAgencyAddress(agency.getAgencyAddress());
        agencyDetailRespVO.setProvince(agency.getProvince());
        agencyDetailRespVO.setCity(agency.getCity());
        agencyDetailRespVO.setDistrict(agency.getDistrict());
        agencyDetailRespVO.setStreet(agency.getStreet());
        agencyDetailRespVO.setDetailAddress(agency.getDetailAddress());
        agencyDetailRespVO.setLongitude(agency.getLongitude());
        agencyDetailRespVO.setLatitude(agency.getLatitude());
        agencyDetailRespVO.setLocationAccuracy(agency.getLocationAccuracy());
        agencyDetailRespVO.setCooperationStatus(agency.getCooperationStatus());
        agencyDetailRespVO.setContractNo(agency.getContractNo());
        agencyDetailRespVO.setContractStartDate(agency.getContractStartDate());
        agencyDetailRespVO.setContractEndDate(agency.getContractEndDate());
        agencyDetailRespVO.setCommissionRate(agency.getCommissionRate());
        agencyDetailRespVO.setReviewStatus(agency.getReviewStatus());
        agencyDetailRespVO.setStatus(agency.getStatus());
        agencyDetailRespVO.setRemark(agency.getRemark());
        agencyDetailRespVO.setCreateTime(agency.getCreateTime());
        agencyDetailRespVO.setUpdateTime(agency.getUpdateTime());

        // 前端展示字段
        agencyDetailRespVO.setLogo("https://example.com/logo.png"); // 需要根据实际数据获取
        agencyDetailRespVO.setBanner("https://example.com/banner.png"); // 需要根据实际数据获取
        agencyDetailRespVO.setRating(new BigDecimal("4.5")); // 需要根据实际数据计算
        agencyDetailRespVO.setReviewCount("2.3万"); // 需要根据实际数据计算
        agencyDetailRespVO.setServedFamilies("12.5万+"); // 需要根据实际数据计算
        agencyDetailRespVO.setEstablishedYears("8年"); // 需要根据成立日期计算
        agencyDetailRespVO.setAddress(agency.getAgencyAddress());
        agencyDetailRespVO.setPhone(agency.getContactPhone());
        agencyDetailRespVO.setStorefrontImages(Arrays.asList(
                "https://example.com/storefront1.png",
                "https://example.com/storefront2.png",
                "https://example.com/storefront3.png",
                "https://example.com/storefront4.png",
                "https://example.com/storefront5.png")); // 需要根据实际数据获取
        agencyDetailRespVO.setCertificates(certificates);

        return agencyDetailRespVO;
    }

    /**
     * 转换为推荐套餐记录列表
     */
    private List<ServicePackageRecommendRespVO.RecommendRecord> convertToRecommendRecords(
            List<ServicePackageDO> recommendList) {
        List<ServicePackageRecommendRespVO.RecommendRecord> records = new ArrayList<>();

        for (ServicePackageDO pkg : recommendList) {
            ServicePackageRecommendRespVO.RecommendRecord record = new ServicePackageRecommendRespVO.RecommendRecord();

            record.setId(pkg.getId());
            record.setName(pkg.getName());
            record.setCategory(pkg.getCategory());
            record.setThumbnail(pkg.getThumbnail());
            record.setPrice(pkg.getPrice());
            record.setOriginalPrice(pkg.getOriginalPrice());
            record.setUnit(pkg.getUnit());
            record.setServiceDuration(pkg.getServiceDuration());
            record.setPackageType(pkg.getPackageType());
            record.setStatus(pkg.getStatus());
            record.setAuditStatus(pkg.getAuditStatus());
            record.setAgencyId(pkg.getAgencyId());
            record.setAgencyName(pkg.getAgencyName());
            record.setCreateTime(pkg.getCreateTime());

            // 前端展示字段
            record.setTitle(pkg.getName());
            record.setPriceDisplay(pkg.getPrice() != null ? pkg.getPrice().toString() : "0");
            record.setImage(pkg.getThumbnail());

            records.add(record);
        }

        return records;
    }

    /**
     * 解析评价标签
     */
    private List<String> parseReviewTags(String reviewTags) {
        if (reviewTags == null || reviewTags.trim().isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(reviewTags, new TypeReference<List<String>>() {
            });
        } catch (Exception e) {
            log.warn("解析评价标签失败: {}", reviewTags, e);
            return new ArrayList<>();
        }
    }

    /**
     * 解析评价图片
     */
    private List<String> parseReviewImages(String reviewImages) {
        if (reviewImages == null || reviewImages.trim().isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(reviewImages, new TypeReference<List<String>>() {
            });
        } catch (Exception e) {
            log.warn("解析评价图片失败: {}", reviewImages, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据套餐ID统计订单数量
     *
     * @param packageId 套餐ID
     * @return 订单数量
     */
    private Integer getServiceCountByPackageId(Long packageId) {
        if (packageId == null) {
            return 0;
        }

        Long count = domesticOrderMapper.selectCount(
                new LambdaQueryWrapperX<DomesticOrderDO>()
                        .eq(DomesticOrderDO::getServicePackageId, packageId)
                        .eq(DomesticOrderDO::getDeleted, false));

        return count != null ? count.intValue() : 0;
    }

    /**
     * 构建套餐规格信息
     *
     * @param servicePackage 服务套餐
     * @return 规格信息
     */
    private String buildSpecification(ServicePackageDO servicePackage) {
        if (servicePackage == null) {
            return "";
        }

        String packageType = servicePackage.getPackageType();
        if (packageType == null || packageType.trim().isEmpty()) {
            return "标准套餐";
        }

        StringBuilder specification = new StringBuilder();

        // 根据套餐类型构建不同的规格信息
        switch (packageType.trim()) {
            case "count-card":
                // 次卡周期套餐：服务次数{serviceTimes}{unit}|有效期{validity_period}{validity_period_unit}
                specification.append("服务次数");
                if (servicePackage.getServiceTimes() != null) {
                    specification.append(servicePackage.getServiceTimes());
                } else {
                    specification.append("0");
                }

                if (servicePackage.getUnit() != null && !servicePackage.getUnit().trim().isEmpty()) {
                    specification.append(servicePackage.getUnit());
                } else {
                    specification.append("次");
                }

                specification.append("|有效期");
                if (servicePackage.getValidityPeriod() != null) {
                    specification.append(servicePackage.getValidityPeriod());
                } else {
                    specification.append("0");
                }

                if (servicePackage.getValidityPeriodUnit() != null
                        && !servicePackage.getValidityPeriodUnit().trim().isEmpty()) {
                    specification.append(servicePackage.getValidityPeriodUnit());
                } else {
                    specification.append("天");
                }
                break;

            case "long-term":
                // 长周期套餐：{serviceTimes}{unit}|{service_interval_type}{service_interval_value}次|每次{single_duration_hours}小时
                if (servicePackage.getServiceTimes() != null) {
                    specification.append(servicePackage.getServiceTimes());
                } else {
                    specification.append("0");
                }

                if (servicePackage.getUnit() != null && !servicePackage.getUnit().trim().isEmpty()) {
                    specification.append(servicePackage.getUnit());
                } else {
                    specification.append("天");
                }

                specification.append("|");

                if (servicePackage.getServiceIntervalType() != null
                        && !servicePackage.getServiceIntervalType().trim().isEmpty()) {
                    specification.append(convertServiceIntervalTypeToText(servicePackage.getServiceIntervalType()));
                } else {
                    specification.append("每天");
                }

                if (servicePackage.getServiceIntervalValue() != null) {
                    specification.append(servicePackage.getServiceIntervalValue());
                } else {
                    specification.append("1");
                }
                specification.append("次|每次");

                if (servicePackage.getSingleDurationHours() != null) {
                    specification.append(servicePackage.getSingleDurationHours());
                } else {
                    specification.append("0");
                }
                specification.append("小时");
                break;

            default:
                // 其他类型，使用原来的逻辑
                if (servicePackage.getCategory() != null && !servicePackage.getCategory().trim().isEmpty()) {
                    specification.append(servicePackage.getCategory());
                }

                if (servicePackage.getPackageType() != null && !servicePackage.getPackageType().trim().isEmpty()) {
                    if (specification.length() > 0) {
                        specification.append(":");
                    }
                    specification.append(servicePackage.getPackageType());
                }

                if (servicePackage.getServiceDuration() != null
                        && !servicePackage.getServiceDuration().trim().isEmpty()) {
                    if (specification.length() > 0) {
                        specification.append(" | ");
                    }
                    specification.append(servicePackage.getServiceDuration());
                }

                if (specification.length() == 0) {
                    return "标准套餐";
                }
                break;
        }

        return specification.toString();
    }

    /**
     * 转换服务间隔类型为中文文本
     *
     * @param serviceIntervalType 服务间隔类型
     * @return 中文文本
     */
    private String convertServiceIntervalTypeToText(String serviceIntervalType) {
        if (serviceIntervalType == null || serviceIntervalType.trim().isEmpty()) {
            return "每天";
        }

        switch (serviceIntervalType.trim().toLowerCase()) {
            case "day":
                return "每天";
            case "weekly":
                return "每周";
            case "monthly":
                return "每月";
            case "year":
                return "每年";
            default:
                return serviceIntervalType; // 如果无法识别，返回原值
        }
    }

    @Override
    @CacheEvict(value = "servicePackage", allEntries = true)
    public void clearServicePackageCache() {
        log.info("清除服务套餐缓存");
    }

}

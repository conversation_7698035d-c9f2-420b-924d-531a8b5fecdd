

### 需求描述

    雇主在小程序端创建订单时，需要以雇主手机号为标识，自动在**线索中心**进行匹配：若存在该雇主的历史线索，则将新订单与之关联并更新线索状态；若不存在，则自动为该雇主创建一条新线索，并将其状态直接标记为"已转化"


    订单号生成逻辑：HM+年月日时分秒毫秒+IdUtil.getSnowflake().nextId() % 1000

    lead_id线索ID生成规则：XS+年月日时分秒毫秒+IdUtil.getSnowflake().nextId() % 1000

    订单主表表结构：
    CREATE TABLE `publicbiz_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `order_type` varchar(30) NOT NULL COMMENT '订单类型：practice-高校实践/training-企业培训/personal-个人培训/domestic-家政服务/certification-考试认证',
  `business_line` varchar(50) NOT NULL COMMENT '业务线：高校实践/企业培训/个人培训与认证/家政服务',
  `opportunity_id` varchar(50) DEFAULT NULL COMMENT '关联商机ID',
  `lead_id` varchar(50) DEFAULT NULL COMMENT '关联线索ID',
  `project_name` varchar(200) DEFAULT NULL COMMENT '项目名称',
  `project_description` text COMMENT '项目描述',
  `start_date` date DEFAULT NULL COMMENT '开始日期',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `total_amount` decimal(12,2) NOT NULL COMMENT '订单总金额',
  `paid_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '已支付金额',
  `refund_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '退款金额',
  `payment_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '支付状态：pending-待支付/paid-已支付/refunded-已退款/cancelled-已取消',
  `order_status` varchar(30) NOT NULL DEFAULT 'draft' COMMENT '订单状态：draft-草稿/pending_approval-待审批/approving-审批中/approved-已批准/rejected-已拒绝/pending_payment-待支付/executing-执行中/completed-已完成/cancelled-已取消',
  `reject_reason` varchar(800) DEFAULT NULL COMMENT '拒绝原因',
  `manager_id` bigint(20) DEFAULT NULL COMMENT '负责人ID',
  `manager_name` varchar(50) DEFAULT NULL COMMENT '负责人姓名',
  `manager_phone` varchar(20) DEFAULT NULL COMMENT '负责人电话',
  `contract_type` varchar(20) DEFAULT 'electronic' COMMENT '合同类型：electronic-电子合同/paper-纸质合同',
  `contract_file_url` varchar(500) DEFAULT NULL COMMENT '合同文件URL',
  `contract_status` varchar(20) DEFAULT 'unsigned' COMMENT '合同状态：unsigned-未签署/signed-已签署/rejected-已拒绝',
  `remark` text COMMENT '备注',
  `settlement_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '结算状态：pending-待结算/processing-结算中/completed-已结算/failed-结算失败',
  `settlement_time` datetime DEFAULT NULL COMMENT '结算时间',
  `settlement_method` varchar(30) DEFAULT NULL COMMENT '结算方式',
  `is_selected_for_reconciliation` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被选中生成对账单：0-未选中，1-已选中',
  `selection_time` datetime DEFAULT NULL COMMENT '选中时间',
  `selector_id` bigint(20) DEFAULT NULL COMMENT '选择人ID',
  `selector_name` varchar(50) DEFAULT NULL COMMENT '选择人姓名',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_business_line` (`business_line`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_settlement_status` (`settlement_status`),
  KEY `idx_is_selected_for_reconciliation` (`is_selected_for_reconciliation`),
  KEY `idx_manager_id` (`manager_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=3025 DEFAULT CHARSET=utf8mb4 COMMENT='订单主表';

订单明细表表结构：
CREATE TABLE `publicbiz_domestic_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `customer_oneid` varchar(50) NOT NULL DEFAULT '' COMMENT '客户OneID GUID',
  `customer_name` varchar(50) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `customer_address` varchar(500) NOT NULL COMMENT '服务地址',
  `customer_remark` varchar(2000) DEFAULT NULL COMMENT '客户备注',
  `service_category_id` bigint(20) NOT NULL COMMENT '服务分类ID',
  `service_category_name` varchar(255) NOT NULL COMMENT '服务分类名称',
  `service_package_id` bigint(20) DEFAULT NULL COMMENT '服务套餐ID',
  `service_package_name` varchar(200) DEFAULT NULL COMMENT '服务套餐名称',
  `service_start_date` date DEFAULT NULL COMMENT '服务开始日期',
  `service_end_date` date DEFAULT NULL COMMENT '服务结束日期',
  `service_duration` varchar(50) DEFAULT NULL COMMENT '服务时长',
  `service_frequency` varchar(50) DEFAULT NULL COMMENT '服务频次',
  `service_package_thumbnail` varchar(500) DEFAULT NULL COMMENT '套餐主图URL',
  `service_package_price` decimal(10,2) NOT NULL COMMENT '套餐价格',
  `service_package_original_price` decimal(10,2) DEFAULT NULL COMMENT '套餐原价',
  `service_package_unit` varchar(20) NOT NULL COMMENT '价格单位：次/项/天/月',
  `service_package_duration` varchar(100) DEFAULT NULL COMMENT '服务时长，如：4小时、26天、90天',
  `service_package_type` varchar(20) NOT NULL COMMENT '套餐类型：long-term-长周期套餐/count-card-次数次卡套餐',
  `service_description` text COMMENT '服务描述',
  `service_details` longtext COMMENT '详细服务内容，富文本格式',
  `service_process` longtext COMMENT '服务流程，富文本格式',
  `purchase_notice` text COMMENT '购买须知',
  `service_times` int(11) DEFAULT '1' COMMENT '服务次数',
  `unit_price` decimal(10,2) NOT NULL COMMENT '单价',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠金额',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实付金额',
  `service_address` varchar(500) NOT NULL COMMENT '服务地址',
  `service_address_detail` varchar(200) DEFAULT NULL COMMENT '详细地址',
  `service_latitude` decimal(10,6) DEFAULT NULL COMMENT '服务地址纬度',
  `service_longitude` decimal(10,6) DEFAULT NULL COMMENT '服务地址经度',
  `service_schedule` json DEFAULT NULL COMMENT '服务时间安排(JSON格式)',
  `practitioner_oneid` varchar(36) DEFAULT NULL COMMENT '服务人员OneID',
  `practitioner_name` varchar(50) DEFAULT NULL COMMENT '服务人员姓名',
  `practitioner_phone` varchar(20) DEFAULT NULL COMMENT '服务人员电话',
  `agency_id` bigint(20) DEFAULT NULL COMMENT '服务机构ID',
  `agency_name` varchar(200) DEFAULT NULL COMMENT '服务机构名称',
  `task_count` int(11) DEFAULT '0' COMMENT '任务总数',
  `completed_task_count` int(11) DEFAULT '0' COMMENT '已完成任务数',
  `task_progress` decimal(5,2) DEFAULT '0.00' COMMENT '任务进度百分比',
  `service_fee` decimal(10,2) DEFAULT NULL COMMENT '服务费',
  `agency_fee` decimal(10,2) DEFAULT NULL COMMENT '机构费',
  `platform_fee` decimal(10,2) DEFAULT NULL COMMENT '平台费',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_customer_name` (`customer_name`),
  KEY `idx_practitioner_oneId` (`practitioner_oneid`),
  KEY `idx_agency_id` (`agency_id`),
  KEY `idx_service_start_date` (`service_start_date`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=2021 DEFAULT CHARSET=utf8mb4 COMMENT='家政服务订单详情表';

订单日志表表结构：
CREATE TABLE `publicbiz_order_log` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
`tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
`creator` varchar(64) DEFAULT NULL COMMENT '创建人',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updater` varchar(64) DEFAULT NULL COMMENT '更新人',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
`order_no` varchar(64) NOT NULL COMMENT '关联工单编号',
`log_type` varchar(30) NOT NULL COMMENT '日志类型：订单创建/编辑/审批通过/审批驳回/确认收款/完成/系统管理员',
`log_title` varchar(200) DEFAULT NULL COMMENT '日志标题',
`log_content` text COMMENT '日志内容',
`old_status` varchar(50) NOT NULL COMMENT '原状态',
`new_status` varchar(50) NOT NULL COMMENT '新状态',
`operator_id` bigint(20) DEFAULT NULL COMMENT '操作人ID',
`operator_name` varchar(50) DEFAULT NULL COMMENT '操作人姓名',
`operator_role` varchar(50) DEFAULT NULL COMMENT '操作人角色',
`related_party_type` varchar(30) DEFAULT NULL COMMENT '关联方类型',
`related_party_name` varchar(100) DEFAULT NULL COMMENT '关联方名称',
  PRIMARY KEY (`id`),
  KEY `idx_work_order_no` (`order_no`),
  KEY `idx_log_type` (`log_type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=67 DEFAULT CHARSET=utf8mb4 COMMENT='订单中心处理日志表';


线索表表结构：
CREATE TABLE `publicbiz_lead_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `lead_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '线索ID，系统生成的唯一标识',
  `customer_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系电话，11位手机号',
  `lead_source` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '其他' COMMENT '线索来源：官网注册、市场活动、公众号文章、视频号、抖音、入驻商家、小红书、微博、知乎、百度推广、微信朋友圈、QQ群/微信群、线下展会、合作伙伴推荐、老客户推荐、电话营销、短信营销、邮件营销、其他',
  `business_module` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '业务模块：高校业务、家政业务、培训业务、认证业务',
  `lead_status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '未处理' COMMENT '线索状态：未处理、跟进中、已转化、无意向',
  `create_method` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '手动创建' COMMENT '创建方式：手动创建、系统导入、API接入',
  `current_owner` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '当前跟进人，可为空表示未分配',
  `current_owner_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '当前跟进人姓名',
  `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注信息，最大500字符',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `creator_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_lead_id` (`lead_id`),
  KEY `idx_customer_phone` (`customer_phone`),
  KEY `idx_lead_source` (`lead_source`),
  KEY `idx_business_module` (`business_module`),
  KEY `idx_lead_status` (`lead_status`),
  KEY `idx_current_owner` (`current_owner`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=2004 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索管理表';

支付记录表：
CREATE TABLE `publicbiz_order_payment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `payment_no` varchar(50) NOT NULL COMMENT '支付单号',
  `payment_type` varchar(20) NOT NULL COMMENT '支付类型：cash-现金/wechat-微信支付/alipay-支付宝/bank_transfer-银行转账/pos-POS机刷卡/other-其他',
  `payment_amount` decimal(12,2) NOT NULL COMMENT '支付金额',
  `payment_status` varchar(20) NOT NULL COMMENT '支付状态：pending-待支付/success-支付成功/failed-支付失败/cancelled-已取消',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作人姓名',
  `payment_remark` text COMMENT '支付备注',
  `transaction_id` varchar(100) DEFAULT NULL COMMENT '第三方交易号',
  `is_escrow` bit(1) DEFAULT b'0' COMMENT '资金托管:0 直接付；1 安心付',
  `platform_payment_no` varchar(50) COMMENT '资金托管:平台支付单号（第二阶段支付）',
  `platform_payment_status` varchar(20) COMMENT '资金托管:平台支付状态',
   `platform_payment_time` datetime COMMENT '资金托管:平台支付时间',
   `receiver_id` bigint(20) COMMENT '收款方用户ID（服务者）',
   `notify_status` varchar(20) DEFAULT 'pending' COMMENT '回调状态：pending/success/failed',
   `notify_time` datetime COMMENT '通联回调时间',
   `channel_type` varchar(50) COMMENT '通联返回渠道信息:支付渠道（如微信/支付宝/银联）',
   `bank_code` varchar(50) COMMENT '通联返回渠道信息:银行编码',
     `payer_account` varchar(100) COMMENT '通联返回渠道信息:付款方账号（如银行卡号/OpenID）';


  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payment_no` (`payment_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_payment_type` (`payment_type`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_payment_time` (`payment_time`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COMMENT='订单支付记录表';



雇主创建订单后需要根据订单的服务次数进行拆单，拆单规则如下：
1. 根据入参serviceTimes的count拆单，
  例如 入参： serviceTimes:["2025-08-25 08:00", "2025-08-27 10:00", "2025-08-29 10:00", "2025-08-30 11:30"]  则需要拆成4个任务单
2. 任务表结构如下：
  CREATE TABLE `publicbiz_domestic_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `domestic_order_id` bigint(20) NOT NULL COMMENT '家政服务订单ID',
  `task_no` varchar(50) NOT NULL COMMENT '任务编号',
  `task_sequence` int(11) NOT NULL COMMENT '任务序号',
  `task_name` varchar(200) NOT NULL COMMENT '任务名称',
  `task_description` text COMMENT '任务描述',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型',
  `task_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '任务状态：pending-待分配/assigned-已分配/in_progress-进行中/completed-已完成/cancelled-已取消',
  `planned_start_time` datetime DEFAULT NULL COMMENT '计划开始时间',
  `planned_end_time` datetime DEFAULT NULL COMMENT '计划结束时间',
  `actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_time` datetime DEFAULT NULL COMMENT '实际结束时间',
  `duration` varchar(50) DEFAULT NULL COMMENT '任务时长',
  `practitioner_oneid` varchar(36) DEFAULT NULL COMMENT '服务人员OneID',
  `practitioner_name` varchar(50) DEFAULT NULL COMMENT '服务人员姓名',
  `practitioner_phone` varchar(20) DEFAULT NULL COMMENT '服务人员电话',
  `schedule_date` date NOT NULL COMMENT '排班日期',
  `service_category_id` bigint(20) NOT NULL COMMENT '服务分类ID',
  `service_category_name` varchar(255) NOT NULL COMMENT '服务分类名称',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `customer_name` varchar(64) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `service_address` varchar(255) NOT NULL COMMENT '服务地址',
  `punch_in_time` datetime DEFAULT NULL COMMENT '打卡开始时间',
  `punch_out_time` datetime DEFAULT NULL COMMENT '打卡结束时间',
  `punch_location` varchar(255) DEFAULT NULL COMMENT '打卡位置',
  `punch_latitude` decimal(10,7) DEFAULT NULL COMMENT '打卡纬度',
  `punch_longitude` decimal(10,7) DEFAULT NULL COMMENT '打卡经度',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_no` (`task_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_domestic_order_id` (`domestic_order_id`),
  KEY `idx_task_sequence` (`task_sequence`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_practitioner_oneId` (`practitioner_oneid`),
  KEY `idx_planned_start_time` (`planned_start_time`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=78 DEFAULT CHARSET=utf8mb4 COMMENT='家政服务任务表';

3. 任务表特殊字段值取值说明(循环入参serviceTimes的值，一个日期时间代表一条任务数据)：
    order_id、order_no：取订单主表的订单ID和订单编号
    domestic_order_id：取订单明细表的订单ID
    task_no：任务编号创建单号规则 TASK+年月日时分秒毫秒+IdUtil.getSnowflake().nextId() % 1000
    task_sequence:任务的序号，需要按照入参的serviceTimes升序排序，最早开始的任务序号就是1
    task_name：任务名称就是取服务套餐的名称
    planned_start_time：计划开始时间取serviceTimes的时间
    planned_end_time：计划结束时间根据serviceTimes的时间加套餐信息表的单次服务时长（single_duration_hours）计算得出
    duration：任务时长 获取套餐信息表的单次服务时长（single_duration_hours）






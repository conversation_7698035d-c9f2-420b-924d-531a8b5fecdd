package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
 * 合同确认 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 合同确认 Request VO")
@Data
public class ContractConfirmReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "合同类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "electronic")
    @NotBlank(message = "合同类型不能为空")
    private String contractType;

    @Schema(description = "合同状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "draft")
    @NotBlank(message = "合同状态不能为空")
    private String contractStatus;

    @Schema(description = "合同备注", example = "电子合同已生成")
    private String contractRemark;
}




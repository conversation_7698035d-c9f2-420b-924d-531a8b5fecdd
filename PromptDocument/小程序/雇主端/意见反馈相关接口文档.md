# 意见反馈相关接口文档

## 1. 接口概述

### 1.1 接口说明
雇主端意见反馈功能，支持提交投诉反馈、查看反馈列表和反馈详情。

### 1.2 接口列表
- 新增意见反馈：`POST /publicbiz/employer/feedback/create`
- 获取意见反馈列表：`GET /publicbiz/employer/feedback/list`
- 获取意见反馈详情：`GET /publicbiz/employer/feedback/detail/{feedbackId}`

## 2. 新增意见反馈接口

### 2.1 接口地址
```
POST /publicbiz/employer/feedback/create
```

### 2.2 请求方式
POST

### 2.3 请求头
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer Token，用户登录凭证 |
| Content-Type | string | 是 | application/json |

### 2.4 请求体参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | string | 是 | 订单ID |
| complaintTypes | array | 是 | 投诉类型列表 |
| complaintContent | string | 是 | 投诉内容，最大500字符 |
| complaintImages | array | 否 | 投诉图片URL列表，最多5张 |

### 2.5 请求示例

```json
{
  "orderId": "TX20241219001",
  "complaintTypes": ["服务态度", "专业能力"],
  "complaintContent": "阿姨在清洁过程中态度敷衍,清洁效果不理想,要求换阿姨",
  "complaintImages": [
    "https://example.com/image1.jpg",
    "https://example.com/image2.jpg"
  ]
}
```

### 2.6 响应参数

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "feedbackId": 1001,
    "orderId": "TX20241219001",
    "createTime": "2025-02-26 14:36:00"
  }
}
```

## 3. 获取意见反馈列表接口

### 3.1 接口地址
```
GET /publicbiz/employer/feedback/list
```

### 3.2 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | integer | 否 | 页码，默认1 |
| size | integer | 否 | 每页数量，默认10 |
| status | string | 否 | 状态筛选：all(全部)、pending(待处理)、processing(处理中)、processed(已处理) |

### 3.3 响应参数

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 2,
    "list": [
      {
        "feedbackId": 1001,
        "orderId": "TX20241219001",
        "complaintTypes": ["服务态度", "专业能力"],
        "complaintContent": "阿姨在清洁过程中态度敷衍,清洁效果不理想,要求换阿姨",
        "status": "pending",
        "createTime": "2025-02-26 14:36:00"
      }
    ]
  }
}
```

## 4. 获取意见反馈详情接口

### 4.1 接口地址
```
GET /publicbiz/employer/feedback/detail/{feedbackId}
```

### 4.2 路径参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| feedbackId | integer | 是 | 反馈ID |

### 4.3 响应参数

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "feedbackId": 1001,
    "status": "pending",
    "complaintInfo": {
      "complaintTypes": ["服务态度", "专业能力"],
      "customerInfo": "张女士 138****5678",
      "orderId": "TX20251297",
      "complaintTime": "2025-04-09 14:09:03",
      "complaintContent": "阿姨在清洁过程中态度敷衍,清洁效果不理想,我要换阿姨.",
      "complaintImages": [
        "https://example.com/image1.jpg",
        "https://example.com/image2.jpg"
      ]
    },
    "serviceInfo": {
      "serviceType": "日常保洁",
      "serviceAddress": "杨浦区莲花小区",
      "serviceTime": "2025-12-19 14:00~17:00",
      "servicePersonnel": "李丽芳"
    }
  }
}
```

## 5. 业务规则


### 5.2 反馈条件验证
1. 用户必须已登录且身份为雇主
2. 订单必须存在且属于当前用户
3. 订单状态必须为已完成或进行中
4. 每个订单只能提交一次反馈

### 5.3 内容规则
1. 投诉内容不能为空，最大500字符
2. 投诉图片可选，最多5张
3. 图片格式支持：jpg、jpeg、png、gif
4. 单张图片大小不超过5MB

### 5.4 状态说明
| 状态 | 说明 |
|------|------|
| pending | 待处理 |
| processing | 处理中 |
| processed | 已处理 |

## 6. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，请重新登录 |
| 403 | 权限不足 |
| 404 | 反馈不存在或订单不存在 |
| 409 | 订单已提交过反馈 |
| 422 | 参数验证失败 |
| 500 | 服务器内部错误 |

## 7. 相关接口

- 获取订单列表：`GET /publicbiz/employer/order/list`
- 获取订单详情：`GET /publicbiz/employer/order/detail/{orderId}`


work_order_no 编号生成规则：WO+年月日时分秒毫秒+IdUtil.getSnowflake().nextId() % 1000
creator/updater 取值逻辑：当前登录用户的id 

任务工单主表表结构：
CREATE TABLE `publicbiz_work_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `work_order_no` varchar(50) NOT NULL COMMENT '工单编号',
  `order_no` varchar(50) NOT NULL COMMENT '关联订单号',
  `work_order_title` varchar(200) NOT NULL COMMENT '工单标题',
  `work_order_content` text NOT NULL COMMENT '工单内容(投诉内容/请假理由)',
  `work_order_type` varchar(30) NOT NULL COMMENT '工单类型：complaint-投诉/substitution_request-换人申请/take_leave-请假/顶岗/separation_application-离职申请',
  `priority` varchar(20) NOT NULL DEFAULT 'medium' COMMENT '优先级：low-低/medium-中/high-高/urgent-紧急',
  `work_order_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '工单状态：pending-待处理/processing-处理中/resolved-已解决/closed-已关闭',
  `assignee_id` bigint(20) DEFAULT NULL COMMENT '负责人ID',
  `assignee_name` varchar(50) DEFAULT NULL COMMENT '负责人姓名',
  `aunt_oneid` varchar(36) NOT NULL DEFAULT '' COMMENT 'OneID GUID',
  `aunt_name` varchar(64) DEFAULT NULL COMMENT '阿姨姓名',
  `leave_type` tinyint(1) DEFAULT NULL COMMENT '请假类型(1-请假,2-调休)',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration_hours` decimal(5,1) DEFAULT NULL COMMENT '请假时长(小时)',
  `duration_days` decimal(3,1) DEFAULT NULL COMMENT '请假天数',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '审批状态(0-审批中,1-已批准,2-已驳回)',
  `approve_time` datetime DEFAULT NULL COMMENT '审批时间',
  `approve_remark` varchar(500) DEFAULT NULL COMMENT '审批备注/处理意见',
  `complaint_type` varchar(50) DEFAULT NULL COMMENT '投诉类型：服务质量/服务态度/守时问题/责任心/其他',
  `complaint_level` varchar(20) DEFAULT NULL COMMENT '投诉等级：low-轻微/medium-中等/high-严重/urgent-紧急',
  `complaint_time` datetime DEFAULT NULL COMMENT '投诉时间',
  `customer_expectation` text COMMENT '客户期望文本内容',
  `complainer_id` bigint(20) DEFAULT NULL COMMENT '投诉人ID',
  `complainer_name` varchar(50) DEFAULT NULL COMMENT '投诉人姓名',
  `agency_id` bigint(20) DEFAULT NULL COMMENT '机构ID',
  `agency_name` varchar(200) DEFAULT NULL COMMENT '机构名称',
  `reassignment_start_date` date DEFAULT NULL COMMENT '重新指派起始日期',
  `new_aunt_oneid` varchar(36) DEFAULT NULL COMMENT '指派新阿姨OneID',
  `new_aunt_name` varchar(50) DEFAULT NULL COMMENT '指派新阿姨名称',
  `reassignment_description` text COMMENT '指派说明内容',
  `reassignment_update_time` datetime DEFAULT NULL COMMENT '指派更新时间',
  `reassignment_reason` varchar(100) DEFAULT NULL COMMENT '转派原因(系统基础数据值)',
  `reassignment_remark` text COMMENT '转派备注',
  `remark` varchar(4000) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_work_order_no` (`work_order_no`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='任务工单表';


任务工单附件表结构：
CREATE TABLE `publicbiz_work_order_attachment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `work_order_no` varchar(50) NOT NULL COMMENT '关联工单编号',
  `file_name` varchar(200) NOT NULL COMMENT '文件名',
  `file_url` varchar(500) NOT NULL COMMENT '文件URL',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型（jpg/png/mp3/pdf等）',
  `file_category` varchar(30) DEFAULT NULL COMMENT '文件分类：evidence-证据材料/contract-合同文件/other-其他',
  `upload_purpose` varchar(100) DEFAULT NULL COMMENT '上传目的（如：投诉证据、服务合同等）',
  PRIMARY KEY (`id`),
  KEY `idx_work_order_no` (`work_order_no`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='工单附件表';
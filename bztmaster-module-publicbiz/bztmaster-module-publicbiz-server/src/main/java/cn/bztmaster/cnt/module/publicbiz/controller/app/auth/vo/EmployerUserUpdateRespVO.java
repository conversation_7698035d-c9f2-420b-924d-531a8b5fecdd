package cn.bztmaster.cnt.module.publicbiz.controller.app.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 雇主端用户信息更新响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "雇主端 APP - 用户信息更新响应")
@Data
public class EmployerUserUpdateRespVO {

    @Schema(description = "用户ID", example = "123456")
    private String userId;

    @Schema(description = "用户头像URL", example = "https://example.com/uploads/avatar/2024/01/user_avatar_123.jpg")
    private String avatar;

    @Schema(description = "用户昵称", example = "张三")
    private String nickname;

    @Schema(description = "更新时间", example = "2024-01-15 10:30:00")
    private LocalDateTime updateTime;

}

package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 进行中订单响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 进行中订单响应")
@Data
public class InProgressOrderRespVO {

    @Schema(description = "进行中订单列表")
    private List<InProgressOrderInfo> data;

    @Schema(description = "订单信息")
    @Data
    public static class InProgressOrderInfo {

        @Schema(description = "订单号", example = "DO202408220001")
        private String orderNo;

        @Schema(description = "订单ID", example = "1001")
        private Long orderId;

        @Schema(description = "服务套餐ID", example = "2001")
        private Long servicePackageId;

        @Schema(description = "服务套餐名称", example = "日常保洁套餐")
        private String servicePackageName;

        @Schema(description = "计划开始时间", example = "2024-08-22 09:00:00")
        private String plannedStartTime;
    }
}

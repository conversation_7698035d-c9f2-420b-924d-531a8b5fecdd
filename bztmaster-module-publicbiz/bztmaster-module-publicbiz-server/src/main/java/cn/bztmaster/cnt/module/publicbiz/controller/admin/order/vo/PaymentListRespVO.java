package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 支付列表 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 支付列表 Response VO")
@Data
public class PaymentListRespVO {

    @Schema(description = "支付ID", example = "1")
    private Long paymentId;

    @Schema(description = "支付单号", example = "PAY123456789")
    private String paymentNo;

    @Schema(description = "支付类型", example = "bank_transfer")
    private String paymentType;

    @Schema(description = "支付金额", example = "5000.00")
    private BigDecimal paymentAmount;

    @Schema(description = "支付状态", example = "paid")
    private String paymentStatus;

    @Schema(description = "支付时间", example = "2024-06-01 10:00:00")
    private String paymentTime;

    @Schema(description = "操作人姓名", example = "系统管理员")
    private String operatorName;
}




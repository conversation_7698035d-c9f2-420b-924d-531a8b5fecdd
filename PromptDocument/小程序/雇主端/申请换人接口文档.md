# 申请换人接口文档

## 1. 接口概述

### 1.1 接口说明
雇主端申请换人功能，支持对当前服务阿姨进行换人申请。

### 1.2 接口地址
```
POST /publicbiz/employer/order/replace
```

### 1.3 请求方式
POST

### 1.4 接口描述
- 支持多种换人原因选择
- 支持自定义原因说明
- 验证订单状态和权限

## 2. 请求参数

### 2.1 请求头
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer Token，用户登录凭证 |
| Content-Type | string | 是 | application/json |

### 2.2 请求体参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | string | 是 | 订单ID |
| replaceReason | string | 是 | 换人原因：attitude(服务态度不好)、quality(服务质量差)、schedule(时间安排冲突)、personal(个人原因)、other(其他原因) |
| customReason | string | 否 | 自定义原因说明（当replaceReason为other时必填） |

### 2.3 请求示例

```json
{
  "orderId": "HT20241201001",
  "replaceReason": "attitude",
  "customReason": ""
}
```

```json
{
  "orderId": "HT20241201001",
  "replaceReason": "other",
  "customReason": "阿姨工作态度不够认真，经常迟到，希望更换更专业的阿姨"
}
```

## 3. 响应参数

### 3.1 响应格式
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "replaceId": 1001,
    "orderId": "HT20241201001",
    "replaceReason": "attitude",
    "customReason": "",
    "status": "pending",
    "createTime": "2025-02-26 14:36:00"
  }
}
```

### 3.2 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | integer | 响应状态码，0表示成功 |
| msg | string | 响应消息 |
| data | object | 响应数据 |
| data.replaceId | integer | 换人申请ID |
| data.orderId | string | 订单ID |
| data.replaceReason | string | 换人原因 |
| data.customReason | string | 自定义原因说明 |
| data.status | string | 申请状态：pending(待处理)、processing(处理中)、approved(已同意)、rejected(已拒绝) |
| data.createTime | string | 申请时间 |

## 4. 业务规则

### 4.1 申请条件验证
1. 用户必须已登录且身份为雇主
2. 订单状态必须为进行中（in_progress）
4. 根据订单号关联 publicbiz_work_order 表，查询是否存在 work_order_type='substitution_request' 且 status=0 且 deleted=0 的工单数据  存在审批中的换人申请时不可再次提交

### 4.2 换人原因规则
支持的换人原因：
- `attitude` - 服务态度不好
- `quality` - 服务质量差
- `schedule` - 时间安排冲突
- `personal` - 个人原因
- `other` - 其他原因

### 4.3 自定义原因规则
1. 当选择"其他原因"时，自定义原因必填
2. 自定义原因最大200字符
3. 不能包含敏感词汇

### 4.4 申请状态说明
| 状态 | 说明 |
|------|------|
| pending | 待处理 |
| processing | 处理中 |
| approved | 已同意 |
| rejected | 已拒绝 |

## 5. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，请重新登录 |
| 403 | 权限不足 |
| 404 | 订单不存在或不属于当前用户 |
| 409 | 订单已提交过换人申请 |
| 422 | 参数验证失败 |
| 423 | 订单状态不允许换人申请 |
| 500 | 服务器内部错误 |


work_order_no 编号生成规则：WO+年月日时分秒毫秒+IdUtil.getSnowflake().nextId() % 1000


换人申请工单表：
CREATE TABLE `publicbiz_work_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `work_order_no` varchar(50) NOT NULL COMMENT '工单编号',
  `order_no` varchar(50) NOT NULL COMMENT '关联订单号',
  `work_order_title` varchar(200) NOT NULL COMMENT '工单标题',
  `work_order_content` text NOT NULL COMMENT '工单内容(投诉内容/请假理由)',
  `work_order_type` varchar(30) NOT NULL COMMENT '工单类型：complaint-投诉/substitution_request-换人申请/take_leave-请假/顶岗/separation_application-离职申请',
  `priority` varchar(20) NOT NULL DEFAULT 'medium' COMMENT '优先级：low-低/medium-中/high-高/urgent-紧急',
  `work_order_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '工单状态：pending-待处理/processing-处理中/resolved-已解决/closed-已关闭',
  `assignee_id` bigint(20) DEFAULT NULL COMMENT '负责人ID',
  `assignee_name` varchar(50) DEFAULT NULL COMMENT '负责人姓名',
  `aunt_oneid` varchar(36) NOT NULL DEFAULT '' COMMENT 'OneID GUID',
  `aunt_name` varchar(64) DEFAULT NULL COMMENT '阿姨姓名',
  `leave_type` tinyint(1) DEFAULT NULL COMMENT '请假类型(1-请假,2-调休)',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration_hours` decimal(5,1) DEFAULT NULL COMMENT '请假时长(小时)',
  `duration_days` decimal(3,1) DEFAULT NULL COMMENT '请假天数',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '审批状态(0-审批中,1-已批准,2-已驳回)',
  `approve_time` datetime DEFAULT NULL COMMENT '审批时间',
  `approve_remark` varchar(500) DEFAULT NULL COMMENT '审批备注/处理意见',
  `complaint_type` varchar(50) DEFAULT NULL COMMENT '投诉类型：服务质量/服务态度/守时问题/责任心/其他',
  `complaint_level` varchar(20) DEFAULT NULL COMMENT '投诉等级：low-轻微/medium-中等/high-严重/urgent-紧急',
  `complaint_time` datetime DEFAULT NULL COMMENT '投诉时间',
  `customer_expectation` text COMMENT '客户期望文本内容',
  `complainer_id` bigint(20) DEFAULT NULL COMMENT '投诉人ID',
  `complainer_name` varchar(50) DEFAULT NULL COMMENT '投诉人姓名',
  `agency_id` bigint(20) DEFAULT NULL COMMENT '机构ID',
  `agency_name` varchar(200) DEFAULT NULL COMMENT '机构名称',
  `reassignment_start_date` date DEFAULT NULL COMMENT '重新指派起始日期',
  `new_aunt_oneid` varchar(36) DEFAULT NULL COMMENT '指派新阿姨OneID',
  `new_aunt_name` varchar(50) DEFAULT NULL COMMENT '指派新阿姨名称',
  `reassignment_description` text COMMENT '指派说明内容',
  `reassignment_update_time` datetime DEFAULT NULL COMMENT '指派更新时间',
  `reassignment_reason` varchar(100) DEFAULT NULL COMMENT '转派原因(系统基础数据值)',
  `reassignment_remark` text COMMENT '转派备注',
  `remark` varchar(4000) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_work_order_no` (`work_order_no`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='任务工单表';

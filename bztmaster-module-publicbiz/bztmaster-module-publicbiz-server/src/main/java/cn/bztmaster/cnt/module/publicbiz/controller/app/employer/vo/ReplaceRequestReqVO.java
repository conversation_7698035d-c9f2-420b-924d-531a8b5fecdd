package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 申请换人请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "申请换人请求 VO")
@Data
public class ReplaceRequestReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "HT20241201001")
    @NotBlank(message = "订单ID不能为空")
    private String orderId;

    @Schema(description = "换人原因：服务态度不好、服务质量差、时间安排冲突、个人原因、其他原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "服务态度不好")
    @NotBlank(message = "换人原因不能为空")
    private String replaceReason;

    @Schema(description = "自定义原因说明（当replaceReason为其他原因时必填）", example = "阿姨工作态度不够认真，经常迟到，希望更换更专业的阿姨")
    private String customReason;
}

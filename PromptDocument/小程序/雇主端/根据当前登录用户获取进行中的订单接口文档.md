订单主表：
CREATE TABLE `publicbiz_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `order_type` varchar(30) NOT NULL COMMENT '订单类型：practice-高校实践/training-企业培训/personal-个人培训/domestic-家政服务/certification-考试认证',
  `business_line` varchar(50) NOT NULL COMMENT '业务线：高校实践/企业培训/个人培训与认证/家政服务',
  `opportunity_id` varchar(50) DEFAULT NULL COMMENT '关联商机ID',
  `lead_id` varchar(50) DEFAULT NULL COMMENT '关联线索ID',
  `project_name` varchar(200) DEFAULT NULL COMMENT '项目名称',
  `project_description` text COMMENT '项目描述',
  `start_date` date DEFAULT NULL COMMENT '开始日期',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `total_amount` decimal(12,2) NOT NULL COMMENT '订单总金额',
  `paid_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '已支付金额',
  `refund_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '退款金额',
  `payment_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '支付状态：pending-待支付/paid-已支付/refunded-已退款/cancelled-已取消',
  `order_status` varchar(30) NOT NULL DEFAULT 'draft' COMMENT '订单状态：draft-草稿/pending_approval-待审批/approving-审批中/approved-已批准/rejected-已拒绝/pending_payment-待支付/executing-执行中/completed-已完成/cancelled-已取消',
  `reject_reason` varchar(800) DEFAULT NULL COMMENT '拒绝原因',
  `manager_id` bigint(20) DEFAULT NULL COMMENT '负责人ID',
  `manager_name` varchar(50) DEFAULT NULL COMMENT '负责人姓名',
  `manager_phone` varchar(20) DEFAULT NULL COMMENT '负责人电话',
  `contract_type` varchar(20) DEFAULT 'electronic' COMMENT '合同类型：electronic-电子合同/paper-纸质合同',
  `contract_file_url` varchar(500) DEFAULT NULL COMMENT '合同文件URL',
  `contract_status` varchar(20) DEFAULT 'unsigned' COMMENT '合同状态：unsigned-未签署/signed-已签署/rejected-已拒绝',
  `remark` text COMMENT '备注',
  `settlement_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '结算状态：pending-待结算/processing-结算中/completed-已结算/failed-结算失败',
  `settlement_time` datetime DEFAULT NULL COMMENT '结算时间',
  `settlement_method` varchar(30) DEFAULT NULL COMMENT '结算方式',
  `is_selected_for_reconciliation` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被选中生成对账单：0-未选中，1-已选中',
  `selection_time` datetime DEFAULT NULL COMMENT '选中时间',
  `selector_id` bigint(20) DEFAULT NULL COMMENT '选择人ID',
  `selector_name` varchar(50) DEFAULT NULL COMMENT '选择人姓名',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_business_line` (`business_line`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_settlement_status` (`settlement_status`),
  KEY `idx_is_selected_for_reconciliation` (`is_selected_for_reconciliation`),
  KEY `idx_manager_id` (`manager_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=3063 DEFAULT CHARSET=utf8mb4 COMMENT='订单主表';

订单明细表：
CREATE TABLE `publicbiz_domestic_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `customer_oneid` varchar(50) NOT NULL DEFAULT '' COMMENT '客户OneID GUID',
  `customer_name` varchar(50) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `customer_address` varchar(500) NOT NULL COMMENT '服务地址',
  `customer_remark` varchar(2000) DEFAULT NULL COMMENT '客户备注',
  `service_category_id` bigint(20) NOT NULL COMMENT '服务分类ID',
  `service_category_name` varchar(255) NOT NULL COMMENT '服务分类名称',
  `service_package_id` bigint(20) DEFAULT NULL COMMENT '服务套餐ID',
  `service_package_name` varchar(200) DEFAULT NULL COMMENT '服务套餐名称',
  `service_start_date` date DEFAULT NULL COMMENT '服务开始日期',
  `service_end_date` date DEFAULT NULL COMMENT '服务结束日期',
  `service_duration` varchar(50) DEFAULT NULL COMMENT '服务时长',
  `service_frequency` varchar(50) DEFAULT NULL COMMENT '服务频次',
  `service_package_thumbnail` varchar(500) DEFAULT NULL COMMENT '套餐主图URL',
  `service_package_price` decimal(10,2) NOT NULL COMMENT '套餐价格',
  `service_package_original_price` decimal(10,2) DEFAULT NULL COMMENT '套餐原价',
  `service_package_unit` varchar(20) NOT NULL COMMENT '价格单位：次/项/天/月',
  `service_package_duration` varchar(100) DEFAULT NULL COMMENT '服务时长，如：4小时、26天、90天',
  `service_package_type` varchar(20) NOT NULL COMMENT '套餐类型：long-term-长周期套餐/count-card-次数次卡套餐',
  `service_description` text COMMENT '服务描述',
  `service_details` longtext COMMENT '详细服务内容，富文本格式',
  `service_process` longtext COMMENT '服务流程，富文本格式',
  `purchase_notice` text COMMENT '购买须知',
  `service_times` int(11) DEFAULT '1' COMMENT '服务次数',
  `unit_price` decimal(10,2) NOT NULL COMMENT '单价',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠金额',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实付金额',
  `service_address` varchar(500) NOT NULL COMMENT '服务地址',
  `service_address_detail` varchar(200) DEFAULT NULL COMMENT '详细地址',
  `service_latitude` decimal(10,6) DEFAULT NULL COMMENT '服务地址纬度',
  `service_longitude` decimal(10,6) DEFAULT NULL COMMENT '服务地址经度',
  `service_schedule` json DEFAULT NULL COMMENT '服务时间安排(JSON格式)',
  `practitioner_oneid` varchar(36) DEFAULT NULL COMMENT '服务人员OneID',
  `practitioner_name` varchar(50) DEFAULT NULL COMMENT '服务人员姓名',
  `practitioner_phone` varchar(20) DEFAULT NULL COMMENT '服务人员电话',
  `agency_id` bigint(20) DEFAULT NULL COMMENT '服务机构ID',
  `agency_name` varchar(200) DEFAULT NULL COMMENT '服务机构名称',
  `task_count` int(11) DEFAULT '0' COMMENT '任务总数',
  `completed_task_count` int(11) DEFAULT '0' COMMENT '已完成任务数',
  `task_progress` decimal(5,2) DEFAULT '0.00' COMMENT '任务进度百分比',
  `service_fee` decimal(10,2) DEFAULT NULL COMMENT '服务费',
  `agency_fee` decimal(10,2) DEFAULT NULL COMMENT '机构费',
  `platform_fee` decimal(10,2) DEFAULT NULL COMMENT '平台费',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_customer_name` (`customer_name`),
  KEY `idx_practitioner_oneId` (`practitioner_oneid`),
  KEY `idx_agency_id` (`agency_id`),
  KEY `idx_service_start_date` (`service_start_date`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=2026 DEFAULT CHARSET=utf8mb4 COMMENT='家政服务订单详情表';


订单任务表：
CREATE TABLE `publicbiz_domestic_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `domestic_order_id` bigint(20) NOT NULL COMMENT '家政服务订单ID',
  `task_no` varchar(50) NOT NULL COMMENT '任务编号',
  `task_sequence` int(11) NOT NULL COMMENT '任务序号',
  `task_name` varchar(200) NOT NULL COMMENT '任务名称',
  `task_description` text COMMENT '任务描述',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型',
  `task_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '任务状态：pending-待分配/assigned-已分配/in_progress-进行中/completed-已完成/cancelled-已取消',
  `planned_start_time` datetime DEFAULT NULL COMMENT '计划开始时间',
  `planned_end_time` datetime DEFAULT NULL COMMENT '计划结束时间',
  `actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_time` datetime DEFAULT NULL COMMENT '实际结束时间',
  `duration` varchar(50) DEFAULT NULL COMMENT '任务时长',
  `practitioner_oneid` varchar(36) DEFAULT NULL COMMENT '服务人员OneID',
  `practitioner_name` varchar(50) DEFAULT NULL COMMENT '服务人员姓名',
  `practitioner_phone` varchar(20) DEFAULT NULL COMMENT '服务人员电话',
  `schedule_date` date NOT NULL COMMENT '排班日期',
  `service_category_id` bigint(20) NOT NULL COMMENT '服务分类ID',
  `service_category_name` varchar(255) NOT NULL COMMENT '服务分类名称',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `customer_name` varchar(64) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `service_address` varchar(255) NOT NULL COMMENT '服务地址',
  `punch_in_time` datetime DEFAULT NULL COMMENT '打卡开始时间',
  `punch_out_time` datetime DEFAULT NULL COMMENT '打卡结束时间',
  `punch_location` varchar(255) DEFAULT NULL COMMENT '打卡位置',
  `punch_latitude` decimal(10,7) DEFAULT NULL COMMENT '打卡纬度',
  `punch_longitude` decimal(10,7) DEFAULT NULL COMMENT '打卡经度',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_no` (`task_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_domestic_order_id` (`domestic_order_id`),
  KEY `idx_task_sequence` (`task_sequence`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_practitioner_oneId` (`practitioner_oneid`),
  KEY `idx_planned_start_time` (`planned_start_time`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=78 DEFAULT CHARSET=utf8mb4 COMMENT='家政服务任务表';


## 根据当前登录用户获取进行中的订单接口

### 接口信息
- **接口名称**: 获取当前用户进行中的订单
- **请求方式**: GET
- **接口地址**: `/publicbiz/employer/order/in-progress`
- **接口描述**: 根据当前登录用户oneid关联订单表的creator字段，获取正在进行中的订单列表

### 请求参数
无请求参数，通过当前登录用户的oneid自动获取

### 响应参数

| 参数名 | 类型 | 是否必填 | 说明 |
|--------|------|----------|------|
| code | Integer | 是 | 响应码，200表示成功 |
| msg | String | 是 | 响应消息 |
| data | Array | 是 | 响应数据 |

#### data字段说明
| 参数名 | 类型 | 是否必填 | 说明 |
|--------|------|----------|------|
| orderNo | String | 是 | 订单号 |
| orderId | Long | 是 | 订单ID |
| servicePackageId | Long | 是 | 服务套餐ID |
| servicePackageName | String | 是 | 服务套餐名称 |
| plannedStartTime | String | 是 | 计划开始时间，格式：yyyy-MM-dd HH:mm:ss |

### 业务逻辑说明
1. 根据当前登录用户的openid，关联`publicbiz_order`表的`creator`字段
2. 查询条件：
   - `publicbiz_order.deleted = 0` (未删除)
   - `publicbiz_order.order_type = 'domestic'` (家政服务订单)
   - `publicbiz_domestic_task.task_status = 'in_progress'` (任务状态为进行中)
   - `publicbiz_domestic_task.deleted = 0` (任务未删除)
3. 关联查询：
   - `publicbiz_order` (订单主表)
   - `publicbiz_domestic_order` (订单明细表)
   - `publicbiz_domestic_task` (订单任务表)
4. 返回字段来源：
   - `order_no`: 来自`publicbiz_order.order_no`
   - `order_id`: 来自`publicbiz_order.id`
   - `service_package_id`: 来自`publicbiz_domestic_order.service_package_id`
   - `service_package_name`: 来自`publicbiz_domestic_order.service_package_name`
   - `planned_start_time`: 来自`publicbiz_domestic_task.planned_start_time`

### 请求示例
```http
GET /publicbiz/employer/order/in-progress HTTP/1.1
Host: 127.0.0.1:48080
Authorization: Bearer <token>
tenant-id: 1
terminal: 10
Content-Type: application/json
```

### 响应示例

#### 成功响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "orderNo": "DO202408220001",
      "orderId": 1001,
      "servicePackageId": 2001,
      "servicePackageName": "日常保洁套餐",
      "plannedStartTime": "2024-08-22 09:00:00"
    },
    {
      "orderNo": "DO202408220002", 
      "orderId": 1002,
      "servicePackageId": 2002,
      "servicePackageName": "深度清洁套餐",
      "plannedStartTime": "2024-08-22 14:00:00"
    }
  ]
}
```

#### 无数据响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": []
}
```

#### 失败响应
```json
{
  "code": 401,
  "msg": "账号未登录"
}
```

### 数据库查询逻辑
```sql
SELECT DISTINCT
    o.order_no,
    o.id as order_id,
    do.service_package_id,
    do.service_package_name,
    dt.planned_start_time
FROM publicbiz_order o
INNER JOIN publicbiz_domestic_order do ON o.id = do.order_id
INNER JOIN publicbiz_domestic_task dt ON o.id = dt.order_id
WHERE o.creator = #{currentUserOpenid}
  AND o.deleted = 0
  AND o.order_type = 'domestic'
  AND do.deleted = 0
  AND dt.deleted = 0
  AND dt.task_status = 'in_progress'
ORDER BY dt.planned_start_time ASC
```
package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 支付确认 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 支付确认 Request VO")
@Data
public class PaymentConfirmReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "支付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "5000.00")
    @NotNull(message = "支付金额不能为空")
    @DecimalMin(value = "0.01", message = "支付金额必须大于0")
    private BigDecimal paymentAmount;

    @Schema(description = "支付方式", example = "bank_transfer")
    private String paymentMethod;

    @Schema(description = "支付备注", example = "银行转账")
    private String paymentRemark;
}




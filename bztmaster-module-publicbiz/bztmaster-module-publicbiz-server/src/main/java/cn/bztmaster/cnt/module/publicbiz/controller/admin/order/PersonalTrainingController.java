package cn.bztmaster.cnt.module.publicbiz.controller.admin.order;

import cn.bztmaster.cnt.module.publicbiz.api.order.dto.*;
import cn.bztmaster.cnt.module.publicbiz.service.order.PersonalTrainingService;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.*;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 个人培训与认证订单管理 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 个人培训与认证订单")
@RestController
@RequestMapping("/publicbiz/order-center/personal-training")
@Validated
public class PersonalTrainingController {

    @Resource
    private PersonalTrainingService personalTrainingService;

    @PostMapping("/page")
    @Operation(summary = "分页查询个人培训与认证订单列表")
    @PreAuthorize("@ss.hasPermission('order:personal-training:query')")
    public CommonResult<PageResult<PersonalTrainingRespDTO>> pagePersonalTraining(@Valid @RequestBody PersonalTrainingPageReqDTO reqDTO) {
        return success(personalTrainingService.pagePersonalTraining(reqDTO));
    }

    @GetMapping("/page-test")
    @Operation(summary = "分页查询个人培训与认证订单列表（测试接口，无权限验证）")
    public CommonResult<PageResult<PersonalTrainingRespDTO>> pagePersonalTrainingTest(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword) {
        
        PersonalTrainingPageReqDTO reqDTO = new PersonalTrainingPageReqDTO();
        reqDTO.setPageNo(page);
        reqDTO.setPageSize(size);
        reqDTO.setKeyword(keyword);
        
        return success(personalTrainingService.pagePersonalTraining(reqDTO));
    }

    @GetMapping("/getByOrderNo/{orderNo}")
    @Operation(summary = "根据订单号获取订单详情")
    @Parameter(name = "orderNo", description = "订单号", required = true)
    @PreAuthorize("@ss.hasPermission('order:personal-training:query')")
    public CommonResult<PersonalTrainingRespDTO> getPersonalTrainingByOrderNo(@PathVariable("orderNo") String orderNo) {
        return success(personalTrainingService.getPersonalTrainingByOrderNo(orderNo));
    }

    @PostMapping("/add")
    @Operation(summary = "新增个人培训与认证订单")
    @PreAuthorize("@ss.hasPermission('order:personal-training:create')")
    public CommonResult<PersonalTrainingSaveRespDTO> createPersonalTraining(@Valid @RequestBody PersonalTrainingSaveReqDTO reqDTO) {
        return success(personalTrainingService.createPersonalTraining(reqDTO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新个人培训与认证订单")
    @PreAuthorize("@ss.hasPermission('order:personal-training:update')")
    public CommonResult<Boolean> updatePersonalTraining(@Valid @RequestBody PersonalTrainingUpdateReqDTO reqDTO) {
        personalTrainingService.updatePersonalTraining(reqDTO);
        return success(true);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除个人培训与认证订单")
    @PreAuthorize("@ss.hasPermission('publicbiz:personal-training:delete')")
    public CommonResult<Boolean> deletePersonalTraining(@PathVariable("id") Long id) {
        personalTrainingService.deletePersonalTraining(id);
        return success(true);
    }

    // ========== 收款相关接口 ==========

    @PostMapping("/confirm-collection")
    @Operation(summary = "确认收款")
    //@PreAuthorize("@ss.hasPermission('publicbiz:personal-training:collection')")
    public CommonResult<PersonalTrainingCollectionRespDTO> confirmCollection(@Valid @RequestBody PersonalTrainingCollectionReqDTO reqDTO) {
        // 设置操作人姓名（TODO: 从安全上下文获取）
        reqDTO.setOperatorName("当前用户");
        
        // 调用服务确认收款
        PersonalTrainingCollectionRespDTO respDTO = personalTrainingService.confirmCollection(reqDTO);
        return success(respDTO);
    }

    @PostMapping("/update-collection")
    @Operation(summary = "更新收款信息")
    //@PreAuthorize("@ss.hasPermission('publicbiz:personal-training:collection')")
    public CommonResult<PersonalTrainingCollectionRespDTO> updateCollection(@Valid @RequestBody PersonalTrainingCollectionReqDTO reqDTO) {
        // 设置操作人姓名（TODO: 从安全上下文获取）
        reqDTO.setOperatorName("当前用户");
        
        // 调用服务更新收款
        PersonalTrainingCollectionRespDTO respDTO = personalTrainingService.updateCollection(reqDTO);
        return success(respDTO);
    }

    @PostMapping("/collection-page")
    @Operation(summary = "分页查询收款记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:personal-training:query')")
    public CommonResult<PageResult<PersonalTrainingCollectionRespDTO>> getCollectionPage(@Valid @RequestBody PersonalTrainingCollectionPageReqDTO pageReqDTO) {
        // 调用服务查询收款记录
        PageResult<PersonalTrainingCollectionRespDTO> pageResult = personalTrainingService.getCollectionPage(pageReqDTO);
        return success(pageResult);
    }

    @GetMapping("/collection-list-by-order-id")
    @Operation(summary = "根据订单ID查询收款记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:personal-training:query')")
    public CommonResult<List<PersonalTrainingCollectionRespDTO>> getCollectionListByOrderId(@RequestParam("orderId") Long orderId) {
        // 调用服务查询收款记录
        List<PersonalTrainingCollectionRespDTO> respList = personalTrainingService.getCollectionListByOrderId(orderId);
        return success(respList);
    }

    @GetMapping("/collection-list-by-order-no")
    @Operation(summary = "根据订单号查询收款记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:personal-training:query')")
    public CommonResult<List<PersonalTrainingCollectionRespDTO>> getCollectionListByOrderNo(@RequestParam("orderNo") String orderNo) {
        // 调用服务查询收款记录
        List<PersonalTrainingCollectionRespDTO> respList = personalTrainingService.getCollectionListByOrderNo(orderNo);
        return success(respList);
    }

    // ========== 审批管理接口 ==========

    @PostMapping("/submit-approval")
    @Operation(summary = "提交审批")
    //@PreAuthorize("@ss.hasPermission('publicbiz:personal-training:approval')")
    public CommonResult<PersonalTrainingApprovalRespDTO> submitApproval(@Valid @RequestBody PersonalTrainingApprovalReqDTO reqDTO) {
        // 设置操作人姓名（TODO: 从安全上下文获取）
        reqDTO.setOperatorName("当前用户");
        
        // 调用服务提交审批
        PersonalTrainingApprovalRespDTO respDTO = personalTrainingService.submitApproval(reqDTO);
        return success(respDTO);
    }

    @PostMapping("/approve")
    @Operation(summary = "审批通过")
    //@PreAuthorize("@ss.hasPermission('publicbiz:personal-training:approval')")
    public CommonResult<Boolean> approve(@Valid @RequestBody PersonalTrainingApprovalReqDTO reqDTO) {
        // 设置操作人姓名（TODO: 从安全上下文获取）
        reqDTO.setOperatorName("当前用户");
        
        // 调用服务审批
        personalTrainingService.approve(reqDTO);
        return success(true);
    }

    @PostMapping("/reject")
    @Operation(summary = "审批拒绝")
    //@PreAuthorize("@ss.hasPermission('publicbiz:personal-training:approval')")
    public CommonResult<Boolean> reject(@Valid @RequestBody PersonalTrainingApprovalReqDTO reqDTO) {
        // 设置操作人姓名（TODO: 从安全上下文获取）
        reqDTO.setOperatorName("当前用户");
        
        // 调用服务审批拒绝
        personalTrainingService.reject(reqDTO);
        return success(true);
    }

    @PostMapping("/approval-page")
    @Operation(summary = "分页查询审批记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:personal-training:query')")
    public CommonResult<PageResult<PersonalTrainingApprovalRespDTO>> getApprovalPage(@Valid @RequestBody PersonalTrainingApprovalPageReqDTO pageReqDTO) {
        // 调用服务查询审批记录
        PageResult<PersonalTrainingApprovalRespDTO> pageResult = personalTrainingService.getApprovalPage(pageReqDTO);
        return success(pageResult);
    }

    // ========== 合同管理接口 ==========

    @PostMapping("/confirm-contract")
    @Operation(summary = "确认合同")
    //@PreAuthorize("@ss.hasPermission('publicbiz:personal-training:contract')")
    public CommonResult<Boolean> confirmContract(@Valid @RequestBody PersonalTrainingContractReqDTO reqDTO) {
        // 调用服务确认合同
        personalTrainingService.confirmContract(reqDTO);
        return success(true);
    }

    @PostMapping("/update-contract")
    @Operation(summary = "更新合同信息")
    //@PreAuthorize("@ss.hasPermission('publicbiz:personal-training:contract')")
    public CommonResult<Boolean> updateContract(@Valid @RequestBody PersonalTrainingContractReqDTO reqDTO) {
        // 调用服务更新合同
        personalTrainingService.updateContract(reqDTO);
        return success(true);
    }

    @GetMapping("/get-contract-info/{orderId}")
    @Operation(summary = "获取合同信息")
    //@Parameter(name = "orderId", description = "订单ID", required = true)
    //@PreAuthorize("@ss.hasPermission('publicbiz:personal-training:query')")
    public CommonResult<PersonalTrainingContractRespDTO> getContractInfo(@PathVariable("orderId") Long orderId) {
        // 调用服务获取合同信息
        PersonalTrainingContractRespDTO respDTO = personalTrainingService.getContractInfo(orderId);
        return success(respDTO);
    }

    // ========== 操作日志接口 ==========

    @PostMapping("/opt-log-page")
    @Operation(summary = "分页查询操作日志")
    // @PreAuthorize("@ss.hasPermission('publicbiz:personal-training:query')")  // 暂时注释掉权限验证
    public CommonResult<PageResult<PersonalTrainingOptLogRespDTO>> getOptLogPage(@Valid @RequestBody PersonalTrainingOptLogPageReqDTO pageReqDTO) {
        // 调用服务查询操作日志
        PageResult<PersonalTrainingOptLogRespDTO> pageResult = personalTrainingService.getOptLogPage(pageReqDTO);
        return success(pageResult);
    }

    @GetMapping("/opt-log-page-test")
    @Operation(summary = "分页查询操作日志（测试接口，GET方法）")
    public CommonResult<PageResult<PersonalTrainingOptLogRespDTO>> getOptLogPageTest(
            @RequestParam("orderNo") String orderNo,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        
        PersonalTrainingOptLogPageReqDTO pageReqDTO = new PersonalTrainingOptLogPageReqDTO();
        pageReqDTO.setOrderNo(orderNo);
        pageReqDTO.setPage(page);
        pageReqDTO.setSize(size);
        
        // 调用服务查询操作日志
        PageResult<PersonalTrainingOptLogRespDTO> pageResult = personalTrainingService.getOptLogPage(pageReqDTO);
        return success(pageResult);
    }

    // ========== 导出接口 ==========

    @PostMapping("/export")
    @Operation(summary = "导出订单列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:personal-training:export')")
    public CommonResult<PersonalTrainingExportRespDTO> export(@Valid @RequestBody PersonalTrainingExportReqDTO reqDTO) {
        // 调用服务导出订单
        PersonalTrainingExportRespDTO respDTO = personalTrainingService.export(reqDTO);
        return success(respDTO);
    }

    // ========== 统计接口 ==========

    @GetMapping("/getStatistics")
    @Operation(summary = "获取订单统计信息")
    @PreAuthorize("@ss.hasPermission('publicbiz:personal-training:query')")
    public CommonResult<PersonalTrainingStatisticsRespDTO> getStatistics() {
        // 调用服务获取统计信息
        PersonalTrainingStatisticsRespDTO respDTO = personalTrainingService.getStatistics();
        return success(respDTO);
    }
}

package cn.bztmaster.cnt.module.publicbiz.service.agency;

import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyScheduleAuntListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyScheduleDayTaskRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyScheduleMonthRespVO;

import java.time.LocalDate;
import java.util.List;

public interface AgencyScheduleService {

    List<AgencyScheduleAuntListRespVO> getAgencyAunts(Long agencyId, String status, String keyword);

    AgencyScheduleMonthRespVO getMonthSchedules(Long agencyId, Integer year, Integer month);

    List<AgencyScheduleDayTaskRespVO> getDayTasks(Long agencyId, Long auntId, LocalDate date);
}



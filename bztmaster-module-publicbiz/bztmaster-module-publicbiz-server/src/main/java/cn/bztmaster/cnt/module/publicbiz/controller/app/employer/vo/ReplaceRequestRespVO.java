package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 申请换人响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "申请换人响应 VO")
@Data
public class ReplaceRequestRespVO {

    @Schema(description = "换人申请ID", example = "1001")
    private Long replaceId;

    @Schema(description = "订单ID", example = "HT20241201001")
    private String orderId;

    @Schema(description = "换人原因", example = "attitude")
    private String replaceReason;

    @Schema(description = "自定义原因说明", example = "")
    private String customReason;

    @Schema(description = "申请状态：pending(待处理)、processing(处理中)、approved(已同意)、rejected(已拒绝)", example = "pending")
    private String status;

    @Schema(description = "申请时间", example = "2025-02-26 14:36:00")
    private String createTime;
}

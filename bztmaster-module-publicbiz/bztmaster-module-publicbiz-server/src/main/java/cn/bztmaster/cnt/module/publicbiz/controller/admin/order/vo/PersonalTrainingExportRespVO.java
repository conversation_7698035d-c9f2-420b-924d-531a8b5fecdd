package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 个人培训与认证订单导出响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 个人培训与认证订单导出响应")
@Data
public class PersonalTrainingExportRespVO {

    @Schema(description = "下载链接", example = "https://example.com/download/orders_20240620.xlsx")
    private String downloadUrl;

    @Schema(description = "文件名", example = "个人培训订单_20240620.xlsx")
    private String fileName;

    @Schema(description = "导出时间", example = "2024-06-20 10:00:00")
    private String exportTime;

    @Schema(description = "导出记录数", example = "100")
    private Integer exportCount;
}


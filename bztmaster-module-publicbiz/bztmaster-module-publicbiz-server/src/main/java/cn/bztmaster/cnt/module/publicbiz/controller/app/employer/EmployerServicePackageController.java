package cn.bztmaster.cnt.module.publicbiz.controller.app.employer;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackageRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackageQueryReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackageDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackageReviewRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.AgencyDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackageRecommendRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.employer.EmployerServicePackageService;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import javax.annotation.security.PermitAll;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 雇主端 - 服务套餐 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "雇主端 - 服务套餐")
@RestController
@RequestMapping("/publicbiz/employer/service-package")
@Validated
public class EmployerServicePackageController {

    @Resource
    private EmployerServicePackageService employerServicePackageService;

    @GetMapping("/packagelists")
    @Operation(summary = "根据分类获取套餐列表")
    @PermitAll
    public CommonResult<PageResult<ServicePackageRespVO>> getServicePackagePage(
            @Valid ServicePackageQueryReqVO reqVO) {
        PageResult<ServicePackageRespVO> pageResult = employerServicePackageService.getServicePackagePage(reqVO);
        return success(pageResult);
    }

    @GetMapping("/detail")
    @Operation(summary = "获取服务套餐详情")
    @Parameter(name = "id", description = "服务套餐ID", required = true, example = "1")
    @PermitAll
    public CommonResult<ServicePackageDetailRespVO> getServicePackageDetail(
            @RequestParam("id") Long id) {
        ServicePackageDetailRespVO detailRespVO = employerServicePackageService.getServicePackageDetail(id);
        return success(detailRespVO);
    }

    @GetMapping("/reviews")
    @Operation(summary = "获取套餐评价列表")
    @Parameter(name = "packageId", description = "服务套餐ID", required = true, example = "1")
    @Parameter(name = "page", description = "页码", example = "1")
    @Parameter(name = "size", description = "每页数量", example = "10")
    @PermitAll
    public CommonResult<ServicePackageReviewRespVO> getServicePackageReviews(
            @RequestParam("packageId") Long packageId,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "size", defaultValue = "10") Integer size) {
        ServicePackageReviewRespVO reviewRespVO = employerServicePackageService.getServicePackageReviews(packageId,
                page, size);
        return success(reviewRespVO);
    }

    @GetMapping("/agency")
    @Operation(summary = "获取套餐所属机构详情")
    @Parameter(name = "packageId", description = "服务套餐ID", required = true, example = "1")
    @PermitAll
    public CommonResult<AgencyDetailRespVO> getAgencyDetail(
            @RequestParam("packageId") Long packageId) {
        AgencyDetailRespVO agencyDetailRespVO = employerServicePackageService.getAgencyDetail(packageId);
        return success(agencyDetailRespVO);
    }

    @GetMapping("/recommendations")
    @Operation(summary = "获取相关推荐套餐")
    @Parameter(name = "packageId", description = "当前套餐ID", required = true, example = "1")
    @Parameter(name = "page", description = "页码", example = "1")
    @Parameter(name = "size", description = "每页数量", example = "10")
    @PermitAll
    public CommonResult<ServicePackageRecommendRespVO> getServicePackageRecommendations(
            @RequestParam("packageId") Long packageId,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "size", defaultValue = "10") Integer size) {
        ServicePackageRecommendRespVO recommendRespVO = employerServicePackageService
                .getServicePackageRecommendations(packageId, page, size);
        return success(recommendRespVO);
    }

    @PostMapping("/clear-cache")
    @Operation(summary = "清除套餐列表缓存")
    @PermitAll
    public CommonResult<Boolean> clearServicePackageCache() {
        employerServicePackageService.clearServicePackageCache();
        return success(true);
    }

}

package cn.bztmaster.cnt.module.publicbiz.controller.app.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 雇主端用户信息更新请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "雇主端 APP - 用户信息更新请求")
@Data
public class EmployerUserUpdateReqVO {

    @Schema(description = "用户头像URL", example = "https://example.com/uploads/avatar/2024/01/user_avatar_123.jpg")
    private String avatar;

    @Schema(description = "用户昵称", example = "张三")
    @Size(max = 20, message = "昵称长度不能超过20个字符")
    private String nickname;

}

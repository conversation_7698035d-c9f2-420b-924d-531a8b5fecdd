package cn.bztmaster.cnt.module.publicbiz.controller.app.agency;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.employment.AgencyService;
import cn.bztmaster.cnt.module.publicbiz.service.employment.AgencyQualificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序 - 机构详情")
@RestController
@RequestMapping("/publicbiz/agency")
@Validated
public class EmployerAgencyController {

    @Resource
    private AgencyService agencyService;

    @Resource
    private AgencyQualificationService agencyQualificationService;

    @GetMapping("/detail")
    @Operation(summary = "获取机构基本信息")
    @ResponseBody
    @PermitAll
    public CommonResult<AgencyDetailRespVO> getAgencyDetail(
            @Parameter(description = "机构ID", required = true) @RequestParam("id") @NotNull Long id) {
        AgencyDetailRespVO agencyDetail = agencyService.getAgencyDetail(id);
        return success(agencyDetail);
    }

    @GetMapping("/aunts")
    @Operation(summary = "获取机构阿姨列表")
    @ResponseBody
    @PermitAll
    public CommonResult<PageResult<AgencyAuntRespVO>> getAgencyAunts(
            @Parameter(description = "机构ID", required = true) @RequestParam("agencyId") @NotNull Long agencyId,
            @Parameter(description = "页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
            @Parameter(description = "每页数量") @RequestParam(value = "size", defaultValue = "10") Integer size) {
        PageResult<AgencyAuntRespVO> pageResult = agencyService.getAgencyAunts(agencyId, page, size);
        return success(pageResult);
    }

    @GetMapping("/packages")
    @Operation(summary = "获取机构服务套餐列表")
    @ResponseBody
    @PermitAll
    public CommonResult<PageResult<AgencyPackageRespVO>> getAgencyPackages(
            @Parameter(description = "机构ID", required = true) @RequestParam("agencyId") @NotNull Long agencyId,
            @Parameter(description = "页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
            @Parameter(description = "每页数量") @RequestParam(value = "size", defaultValue = "10") Integer size) {
        PageResult<AgencyPackageRespVO> pageResult = agencyService.getAgencyPackages(agencyId, page, size);
        return success(pageResult);
    }

    @GetMapping("/review-stats")
    @Operation(summary = "获取机构评价统计")
    @ResponseBody
    @PermitAll
    public CommonResult<AgencyReviewStatsRespVO> getAgencyReviewStats(
            @Parameter(description = "机构ID", required = true) @RequestParam("agencyId") @NotNull Long agencyId) {
        AgencyReviewStatsRespVO reviewStats = agencyService.getAgencyReviewStats(agencyId);
        return success(reviewStats);
    }

    @GetMapping("/qualifications")
    @Operation(summary = "获取机构资质文件列表")
    @ResponseBody
    @PermitAll
    public CommonResult<AgencyQualificationRespVO> getAgencyQualifications(
            @Parameter(description = "机构ID", required = true) @RequestParam("agencyId") @NotNull Long agencyId) {
        AgencyQualificationRespVO qualifications = agencyQualificationService.getAgencyQualificationsForApp(agencyId);
        return success(qualifications);
    }

    // ========== 机构注册相关接口 ==========

    @PostMapping("/register")
    @Operation(summary = "机构注册申请")
    @ResponseBody
    @PermitAll
    public CommonResult<AgencyRegisterResponseVO> registerAgency(
            @Valid @RequestBody AgencyRegisterRequestVO request) {
        AgencyRegisterResponseVO response = agencyService.registerAgency(request);
        return success(response);
    }

    @PostMapping("/upload")
    @Operation(summary = "上传机构证照文件")
    @ResponseBody
    @PermitAll
    public CommonResult<FileUploadResponseVO> uploadFile(
            @Parameter(description = "文件", required = true) @RequestParam("file") MultipartFile file,
            @Parameter(description = "文件类型", required = true) @RequestParam("fileType") String fileType,
            @Parameter(description = "文件分类", required = true) @RequestParam("fileCategory") String fileCategory,
            @Parameter(description = "机构ID（注册时可为空）") @RequestParam(value = "agencyId", required = false) Long agencyId,
            @Parameter(description = "临时会话ID（注册时使用）") @RequestParam(value = "sessionId", required = false) String sessionId) {
        FileUploadResponseVO response = agencyService.uploadAgencyFile(file, fileType, fileCategory, agencyId, sessionId);
        return success(response);
    }

    @GetMapping("/register/status/{agencyId}")
    @Operation(summary = "查询机构注册申请状态")
    @ResponseBody
    @PermitAll
    public CommonResult<AgencyRegisterStatusRespVO> getRegisterStatus(
            @Parameter(description = "机构ID", required = true)
            @PathVariable("agencyId") @NotNull(message = "机构ID不能为空") Long agencyId) {
        AgencyRegisterStatusRespVO response = agencyService.getRegisterStatus(agencyId);
        return success(response);
    }
}
